{"name": "hadar-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.12", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.45.4", "@tanstack/react-table": "^8.20.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "file-saver": "^2.0.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.452.0", "next": "14.2.15", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-hook-form": "^7.53.0", "tailwind-merge": "^2.5.3", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20", "@types/react": "^18.3.18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.15", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}