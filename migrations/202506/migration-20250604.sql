drop function public.get_lotes_entrada_estoque;
create or replace function public.get_lotes_entrada_estoque(
  _offset integer default 0,
  _limit integer default 50,
  _filters jsonb default '{}'::jsonb,
  _sorts text default 'l.created_at desc'
)
returns table (
  id uuid,
  created_at timestamp with time zone,
  numero_lote text,
  total_count integer
)
language plpgsql
as $$
declare
  _sql text;
  _count_sql text;
  _filters_sql text;
  _total integer;
begin
  -- <PERSON><PERSON>r filt<PERSON> em SQL
  _filters_sql := coalesce((
    select string_agg(
      case 
        when key = 'numero_lote' then format('and %I ilike %L', key, '%' || value::text || '%')
        else format('and %I = %L', key, value::text)
      end,
      ' '
    )
    from jsonb_each_text(_filters)
  ), '');

  -- Contagem total
  _count_sql := format($count$
    select count(*) from (
      select 1
      from lotes l
      left join itens_entrada_estoque iee on iee.lote_id = l.id
      left join itens_saida_estoque ise on ise.lote_id = l.id
      left join itens_desalocacao id on id.lote_id = l.id
      left join movimentacoes_internas mi on mi.desalocacao_id = id.desalocacao_id
      where 1 = 1
      %s
      group by l.id, l.numero_lote
      having
        max(iee.created_at) is null
        or (
          max(iee.created_at) is not null
          and (
            max(ise.created_at) is not null and max(id.created_at) > max(ise.created_at)
          )
          and (
            max(mi.created_at) is null or max(mi.created_at) < max(id.created_at)
          )
        )
    ) sub
  $count$, _filters_sql);

  execute _count_sql into _total;

  -- Consulta paginada com total_count por linha
  _sql := format($f$
    select
      l.id,
      l.created_at,
      l.numero_lote,
      %s as total_count
    from
      lotes l
      left join itens_entrada_estoque iee on iee.lote_id = l.id
      left join itens_saida_estoque ise on ise.lote_id = l.id
      left join itens_desalocacao id on id.lote_id = l.id
      left join movimentacoes_internas mi on mi.desalocacao_id = id.desalocacao_id
    where
      1 = 1
      %s
    group by
      l.id,
      l.numero_lote
    having
      max(iee.created_at) is null
      or (
        max(iee.created_at) is not null
        and (
          max(ise.created_at) is not null and max(id.created_at) > max(ise.created_at)
        )
        and (
          max(mi.created_at) is null or max(mi.created_at) < max(id.created_at)
        )
      )
    order by %s
    offset %s
    limit %s
  $f$, _total, _filters_sql, _sorts, _offset, _limit);

  return query execute _sql;
end;
$$;



-- VIEW alocacoes
CREATE OR REPLACE VIEW vw_alocacoes_list AS
SELECT
  a.id,
  a.numero_controle,
  a.created_at,
  TO_CHAR(a.created_at, 'DD/MM/YYYY') AS created_at_str,
  r.nome AS rua,
  b.nome AS bloco,
  ba.nome AS baia,
  u.email AS user_email
FROM alocacoes a
LEFT JOIN user_profiles u ON u.id = a.user_id
LEFT JOIN ruas r ON r.id = a.rua_id
LEFT JOIN blocos b ON b.id = a.bloco_id
LEFT JOIN baias ba ON ba.id = a.baia_id
WHERE a.ativo IS TRUE;

-- VIEW desalocacoes
CREATE OR REPLACE VIEW vw_desalocacoes_list AS
SELECT
  d.id,
  d.numero_controle,
  d.created_at,
  TO_CHAR(d.created_at, 'DD/MM/YYYY') AS created_at_str,
  u.email AS user_email
FROM desalocacoes d
LEFT JOIN user_profiles u ON u.id = d.user_id
WHERE d.ativo IS TRUE;

-- VIEW entradas_estoque
CREATE OR REPLACE VIEW vw_entradas_estoque_list AS
SELECT
  e.id,
  e.created_at,
  TO_CHAR(e.created_at, 'DD/MM/YYYY') AS created_at_str,
  e.numero_controle,
  u.email AS user_email
FROM entradas_estoque e
LEFT JOIN user_profiles u ON u.id = e.user_id
WHERE e.ativo IS TRUE;

-- VIEW saidas_estoque
CREATE OR REPLACE VIEW vw_saidas_estoque_list AS
SELECT
  s.id,
  s.created_at,
  TO_CHAR(s.created_at, 'DD/MM/YYYY') AS created_at_str,
  s.numero_controle,
  u.email AS user_email,
  op.op
FROM saidas_estoque s
LEFT JOIN user_profiles u ON u.id = s.user_id
LEFT JOIN ordens_producao op ON op.id = s.ordem_producao_id
WHERE s.ativo IS TRUE;