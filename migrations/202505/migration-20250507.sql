-- Adiciona um novo campo ensaio na tabela ordens_producao
ALTER TABLE public.ordens_producao ADD COLUMN ensaio text;

-- Cria uma nova view para listar as ordenagens com os novos campos codlocal e ensaio
create or replace view public.vw_ordenagens as
select distinct ordenagens.*, ordens_producao.op, user_profiles.email, envelopes_contagem.codlocal, ordens_producao.ensaio
from ordenagens
join ordens_producao on ordens_producao.id = ordenagens.ordem_producao_id
join user_profiles on user_profiles.id = ordenagens.user_id
join parcelas_ordem_producao on parcelas_ordem_producao.ordem_producao_id = ordens_producao.id
join envelopes_contagem on envelopes_contagem.parcela_ordem_producao_id = parcelas_ordem_producao.id
where ordenagens.ativo = true;
