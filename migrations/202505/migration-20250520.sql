CREATE OR REPLACE FUNCTION validate_ordenagem_envelope()
RETURNS trigger AS $$
DECLARE
  max_envelope_contagem text;
BEGIN
  -- Validação 1: Verifica se o novo último envelope excede o envelope da OP
  SELECT MAX(ec.numero_envelope)
  INTO max_envelope_contagem
  FROM envelopes_contagem ec
  JOIN parcelas_ordem_producao pop ON pop.id = ec.parcela_ordem_producao_id
  WHERE pop.ordem_producao_id = NEW.ordem_producao_id;

  IF NEW.numero_ultimo_envelope > max_envelope_contagem THEN
    RAISE EXCEPTION 
      'Erro: número do último envelope informado é maior que o número do último envelope da OP.';
  END IF;

  -- Validação 2: Sobreposição de intervalos com outras ordenagens da mesma OP
  IF EXISTS (
    SELECT 1
    FROM ordenagens o
    WHERE o.ordem_producao_id = NEW.ordem_producao_id
      AND o.id <> NEW.id
      AND NOT (
        NEW.numero_ultimo_envelope < o.numero_primeiro_envelope OR
        NEW.numero_primeiro_envelope > o.numero_ultimo_envelope
      )
  ) THEN
    RAISE EXCEPTION 
      'Erro: intervalo de envelopes informado (% - %) se sobrepõe com outro registro da mesma OP.',
      NEW.numero_primeiro_envelope, NEW.numero_ultimo_envelope;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;


DROP TRIGGER IF EXISTS trg_validate_ordenagem_envelope ON ordenagens;

CREATE TRIGGER trg_validate_ordenagem_envelope
BEFORE INSERT OR UPDATE ON ordenagens
FOR EACH ROW
EXECUTE FUNCTION validate_ordenagem_envelope();


create view public.vw_numero_envelopes_op as
select ordens_producao.id, ordens_producao.op, min(envelopes_contagem.numero_envelope), max(envelopes_contagem.numero_envelope)
from ordens_producao
join parcelas_ordem_producao on parcelas_ordem_producao.ordem_producao_id = ordens_producao.id 
join envelopes_contagem on envelopes_contagem.parcela_ordem_producao_id = parcelas_ordem_producao.id
group by ordens_producao.id, ordens_producao.op