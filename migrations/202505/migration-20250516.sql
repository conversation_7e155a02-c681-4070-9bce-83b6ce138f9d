-- Definitions da function "get_lotes_disponiveis_saida" (adicionado filtro de op que recebe no parametro)
select
   numero_lote,
   id
from
(select
   l.numero_lote,
   l.id,
   max(iee.created_at) as ultima_data_entrada,
   max(ise.created_at) as ultima_data_saida,
   max(ia.created_at) as ultima_data_alocacao
from public.parcelas_ordem_producao as pop
  join lotes_ordem_beneficiamento as lob on lob.parcela_id = pop.parcela_id
  join lotes as l on l.id = lob.lote_id
  join ordens_producao as op on op.id = pop.ordem_producao_id
  join itens_alocacao as ia on ia.lote_id = l.id
  left join itens_entrada_estoque as iee on iee.lote_id = l.id
  left join itens_saida_estoque as ise on ise.lote_id = l.id
where
  pop.ativo = true
  and pop.parcela_id is not null
  and op.op = numero_op
group by
   l.numero_lote,
   l.id
order by
   l.numero_lote) as dados
where
  ultima_data_alocacao is not null and ultima_data_saida is null
  OR ultima_data_alocacao > ultima_data_saida
order by
   numero_lote