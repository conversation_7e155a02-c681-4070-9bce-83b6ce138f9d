import { useEffect, useState } from "react";

/**
 * Hook para debounce de valores
 * @param value Valor a ser debounced
 * @param delay Tempo de delay em milissegundos (250ms por padrão)
 * @returns Valor debounced
 */
export function useDebounce<T>(value: T, delay: number = 250): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
} 