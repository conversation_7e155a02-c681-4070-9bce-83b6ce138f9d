import { useEffect, useState } from "react";
import { EnderecoEstoque } from "@/components/planta-estoque/types";
import {
  getRuas,
  getBlocos,
  getBaias,
  getLotesEmEstoque,
} from "@/service/alocacoes-service";

/**
 * Hook para montar todos os endereços possíveis do estoque (rua, bloco, baia)
 * e cruzar com os lotes em estoque para determinar o status de cada posição.
 */
export function usePlantaEstoque() {
  const [enderecos, setEnderecos] = useState<EnderecoEstoque[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<any>(null);

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      try {
        const [ruas, blocos, baias, lotes] = await Promise.all([
          getRuas(),
          getBlocos(),
          getBaias(),
          getLotesEmEstoque(),
        ]);

        const allEnderecos: EnderecoEstoque[] = [];
        (ruas ?? []).forEach((rua: any) => {
          (blocos ?? []).forEach((bloco: any) => {
            (baias ?? []).forEach((baia: any) => {
              const lote = (lotes ?? []).find(
                (l: any) =>
                  l.rua_id === rua.id &&
                  l.bloco_id === bloco.id &&
                  l.baia_id === baia.id
              );
              allEnderecos.push({
                rua: { id: rua.id, nome: rua.nome },
                bloco: { id: bloco.id, nome: bloco.nome },
                baia: { id: baia.id, nome: baia.nome },
                status: lote ? "ocupado" : "livre",
                lotes: lote
                  ? [{
                      id: lote.id,
                      numero_lote: lote.numero_lote,
                      created_at: lote.created_at ? new Date(lote.created_at) : new Date(),
                      updated_at: lote.updated_at ? new Date(lote.updated_at) : new Date(),
                      ativo: lote.ativo !== undefined ? lote.ativo : true,
                    }]
                  : undefined,
              });
            });
          });
        });

        setEnderecos(allEnderecos);
        setLoading(false);
      } catch (err) {
        setError(err);
        setLoading(false);
      }
    }
    fetchData();
  }, []);

  return { enderecos, loading, error };
}
