import { createClient } from '../../utils/supabase/client'
import { useEffect, useState } from 'react'

interface UseAuthReturn {
  isAdmin: boolean
  isLoading: boolean
}

export function useAuth(): UseAuthReturn {
  const [isAdmin, setIsAdmin] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function checkAdmin() {
      try {
        const supabase = createClient()
        const { data: { user } } = await supabase.auth.getUser()

        if (user) {
          const { data: profile } = await supabase
            .from('user_profiles')
            .select('role')
            .eq('id', user.id)
            .single()

          setIsAdmin(profile?.role === 'admin')
        }
      } catch (error) {
        console.error('Erro ao verificar permissões:', error)
      } finally {
        setIsLoading(false)
      }
    }

    checkAdmin()
  }, [])

  return { isAdmin, isLoading }
} 