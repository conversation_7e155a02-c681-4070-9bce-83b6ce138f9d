export interface Desalocacao {
  id: string;
  created_at: Date;
  updated_at: Date;
  ativo: boolean;
  user_id: string;
  numero_controle: string;
}

export interface DesalocacaoList {
  id: string;
  created_at: Date;
  user_email: string;
  numero_controle: string;
}

export interface DesalocacaoItem {
  id: string | undefined;
  created_at: Date | undefined;
  updated_at: Date | undefined;
  ativo: boolean | undefined;
  desalocacao_id: string | undefined;
  lote_id: string | undefined;
}

export interface LoteAlocado {
  lote_id: string;
  numero_lote: string;
  ultima_entrada: string;
  ultima_alocacao: string;
  ultima_saida: string;
  ultima_desalocacao: string;
}
