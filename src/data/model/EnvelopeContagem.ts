export interface EnvelopeContagem {
  id: string | undefined;
  created_at: Date | undefined;
  updated_at: Date | undefined;
  ativo: boolean | undefined;
  parcela_ordem_producao_id: string | undefined;
  numero_envelope: string | undefined;
  user_id: string | undefined;
  observacoes: string | undefined;
  concluido: boolean | undefined;
}

export interface DocumentoEnvelopeContagem {
  npe: string | undefined;
  nca: string | undefined;
  status: string | undefined;
  gli: string | undefined;
  codlocal: string | undefined;
  linhagem: string | undefined;
}
