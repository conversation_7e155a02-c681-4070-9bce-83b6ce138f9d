export interface Alocacao {
  id: string;
  created_at: Date;
  numero_controle: string;
  user_email: string;
  rua: string;
  bloco: string;
  baia: string;
  lotes: {
    id: string;
    numero_lote: string;
  }[];
}

export interface AlocacaoListItem {
  id: string;
  created_at: Date;
  numero_controle: string;
  user_email: string;
  rua: string;
  bloco: string;
  baia: string;
}

export interface AlocacaoItem {
  id: string | undefined;
  alocacao: string | undefined;
  lote: string | undefined;
  rua: string | undefined;
  bloco: string | undefined;
  baia: string | undefined;
}

export interface Rua {
  id: string;
  created_at: string;
  updated_at: string;
  ativo: boolean;
  nome: string;
}

export interface Baia {
  id: string;
  created_at: string;
  updated_at: string;
  ativo: boolean;
  nome: string;
}

export interface Bloco {
  id: string;
  created_at: string;
  updated_at: string;
  ativo: boolean;
  nome: string;
}

export interface LoteAlocacao {
  lote_id: string;
  numero_lote: string;
  ultima_entrada: Date;
  ultima_alocacao: Date;
  ultima_saida: Date;
  ultima_desalocacao: Date;
}
