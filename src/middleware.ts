import { createClient } from "../utils/supabase/middleware";
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export async function middleware(request: NextRequest) {
  try {
    const supabase = createClient(request);

    // Verifica se o usuário está autenticado
    const {
      data: { user },
    } = await supabase.auth.getUser();

    // Se não estiver autenticado, redireciona para o login
    if (!user) {
      return NextResponse.redirect(new URL("/login", request.url));
    }

    // Busca o perfil do usuário com role e permitted_routes
    const { data: profile } = await supabase
      .from("user_profiles")
      .select("role, permitted_routes")
      .eq("id", user.id)
      .single();

    if (!profile) {
      return NextResponse.redirect(new URL("/login", request.url));
    }

    // Admin tem acesso total
    if (profile.role === "admin") {
      return NextResponse.next();
    }

    // Verifica se a rota está permitida para o usuário
    const pathname = request.nextUrl.pathname;

    // Permite acesso ao login e perfil para todos
    if (pathname.startsWith("/login") || pathname.startsWith("/perfil")) {
      return NextResponse.next();
    }

    // Verifica se a rota está na lista de permitted_routes
    const allowed = profile.permitted_routes?.some(
      (route: string) =>
        pathname === `/${route}` || pathname.startsWith(`/${route}/`)
    );

    if (!allowed) {
      const deniedUrl = new URL("/acesso-negado", request.url);
      return NextResponse.redirect(deniedUrl);
    } else {
      return NextResponse.next();
    }
  } catch (e) {
    return NextResponse.redirect(new URL("/login", request.url));
  }
}

export const config = {
  matcher: [
    "/beneficiamento/:path*",
    "/bi/:path*",
    "/configuracoes/:path*",
    "/contagem/:path*",
    "/estoque/:path*",
    "/estratificacao/:path*",
    "/expedicao/:path*",
    "/file-upload/:path*",
    "/home/<USER>",
    "/ordenagem/:path*",
    "/perfil/:path*",
    "/selecao/:path*",
    "/usuarios/:path*",
  ],
};
