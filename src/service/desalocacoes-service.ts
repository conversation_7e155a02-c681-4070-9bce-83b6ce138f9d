'use server'

import { createClient } from '../../utils/supabase/server';
import { Lote } from '@/data/model/Lote';

export async function getDesalocacoes() {
  const supabase = createClient()

  const { data, error } = await supabase
    .from('desalocacoes')
    .select(`id, created_at, numero_controle, user_id, user_profiles (email)`)
    .eq('ativo', true);

  if (!error && data) {
    // retira o objeto aninhado
    const formattedData = data.map((item: any) => ({
      ...item,
      email: item.user_profiles.email,
    }));

    // Remover o campo `user_profiles` agora desnecessário
    formattedData.forEach(
      (item: { user_profiles: any }) => delete item.user_profiles
    );

    return formattedData;
  } else {
    throw new Error(error.message);
  }
}

export async function getDesalocacaoById(
  desalocacao_id: string
): Promise<any | null> {
  const supabase = createClient()

  const { data, error } = await supabase
    .from('desalocacoes')
    .select(`id, created_at, numero_controle, user_profiles (email)`)
    .eq('id', desalocacao_id);

  if (!error && data) {
    // retira o objeto aninhado
    const formattedData = data.map((item: any) => ({
      ...item,
      email: item.user_profiles.email,
    }));

    // Remover o campo `user_profiles` agora desnecessário
    formattedData.forEach((item) => delete item.user_profiles);

    return formattedData[0];
  } else {
    throw new Error(error.message);
  }
}

export async function getDesalocacaoItens(
  desalocacao_id: string
): Promise<any[]> {
  const supabase = createClient()

  const { data, error } = await supabase
    .from('itens_desalocacao')
    .select(`id, lotes (numero_lote)`)
    .eq('desalocacao_id', desalocacao_id)
    .eq('ativo', true);

  if (!error && data) {
    // retira o objeto aninhado
    const formattedData = data.map((item: any) => ({
      ...item,
      numero_lote: item.lotes.numero_lote,
    }));

    // Remover o campo `lotes` agora desnecessário
    formattedData.forEach((item) => delete item.lotes);

    return formattedData;
  } else {
    throw new Error(error.message);
  }
}

export async function getDesalocacaoId(
): Promise<string | null> {
  const supabase = createClient()

  const { data, error } = await supabase
    .from('desalocacoes')
    .insert({})
    .select();

  if (!error && data) {
    return data[0].id;
  } else {
    throw new Error(error.message);
  }
}

export async function insertDesalocacao(
  itemsDesalocacao: any[]
) {
  const supabase = createClient()

  const { data, error } = await supabase
    .from('itens_desalocacao')
    .insert(itemsDesalocacao)
    .select();

  if (!error && data) {
    return true;
  } else {
    throw new Error(error.message);
  }
}

export async function getLotesParaDesalocacaoInterna() {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('vw_lotes_para_desalocacao_interna')
    .select()
    .is('ativo', true);

  if (!error && data) {
    const lotes: Lote[] = data.map((item: any) => ({
      id: item.id,
      created_at: item.created_at,
      updated_at: item.updated_at,
      ativo: item.ativo,
      numero_lote: item.numero_lote
    }));

    return lotes;
  } else {
    throw new Error(error.message);
  }
}

export async function getLotesParaDesalocacaoExterrna() {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('vw_lotes_para_desalocacao_externa')
    .select()
    .is('ativo', true);

  if (!error && data) {
    const lotes: Lote[] = data.map((item: any) => ({
      id: item.id,
      created_at: item.created_at,
      updated_at: item.updated_at,
      ativo: item.ativo,
      numero_lote: item.numero_lote
    }));

    return lotes;
  } else {
    throw new Error(error.message);
  }
}
