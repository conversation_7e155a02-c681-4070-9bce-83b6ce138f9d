'use server'

import { createClient } from "../../utils/supabase/server";

export async function getDataFromSupabaseByField(
  tableName: string,
  filterField: any = '',
  filterFieldValue: any = ''
) {
  const supabase = createClient();
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select()
      .in(filterField, filterFieldValue);

    if (!error && data) {
      return data;
    }
  } catch (error) {
    console.error('Erro ao receber dados do Supabase:', error);
  }
}
