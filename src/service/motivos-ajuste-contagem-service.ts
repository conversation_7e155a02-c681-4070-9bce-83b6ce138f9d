'use server'

import { createClient } from '../../utils/supabase/server';

export interface MotivoAjusteContagem {
  id: string;
  nome: string;
}

export async function getMotivosAjusteContagem(): Promise<MotivoAjusteContagem[] | undefined> {
  const supabase = createClient();
  try {
    const { data, error } = await supabase
      .from('motivo_ajuste_contagem')
      .select('id, nome');

    if (!error && data) {
      return data;
    }
  } catch (error) {
    console.error('Erro ao receber dados do Supabase:', error);
  }
}
