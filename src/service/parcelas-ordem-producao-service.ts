import { createClient } from "../../utils/supabase/client";

export async function getMinMaxNumeroEnvelopeByOrdemProducaoId(
  ordemProducaoId: string
): Promise<{ min: string; max: string } | undefined> {
  const supabase = createClient();
  try {
    const { data, error } = await supabase
      .from("vw_numero_envelopes_op")
      .select("min, max")
      .eq("id", ordemProducaoId);

    if (!error && data) {
      return data[0];
    }
  } catch (error) {
    console.error("Erro ao receber dados do Supabase:", error);
  }
}
