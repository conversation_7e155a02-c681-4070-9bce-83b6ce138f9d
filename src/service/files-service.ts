"use server";

import { HarvestOrder } from "@/data/model/HarvestOrder";
import { createClient } from "../../utils/supabase/server";
import { OrdemColheita } from "@/data/model/OrdemColheita";
import { ProcessingOrder } from "@/data/model/ProcessingOrder";
import { OrdemBeneficiamento } from "@/data/model/OrdemBeneficiamento";
import { ProductionOrder } from "@/data/model/ProductionOrder";
import { DocumentoEnvelopeContagem } from "@/data/model/EnvelopeContagem";
import { DocumentoExpedicao } from "@/data/model/CaixaExpedicao";

export async function persistHarvestOrderToSupabase(
  items: HarvestOrder[],
  fileName: string
) {
  try {
    const dataIOC = await insertDataToSupabase(
      [{ nome_arquivo: fileName }],
      "importacoes_ordens_colheita"
    );

    if (dataIOC) {
      const agrupadosPorLoteColheita = items.reduce(
        (acc: { [key: string]: HarvestOrder[] }, obj: HarvestOrder) => {
          if (!acc[obj.lote_colheita]) {
            acc[obj.lote_colheita] = []; // Cria uma lista para o 'lote_colheita' se não existir
          }
          acc[obj.lote_colheita].push(obj); // Adiciona o objeto na lista correspondente
          return acc;
        },
        {}
      );

      const listaComIndices = Object.entries(agrupadosPorLoteColheita).map(
        ([lote_colheita, items]) => ({
          lote_colheita,
          items,
        })
      );

      //console.log(JSON.stringify(listaComIndices, null, 2));

      listaComIndices.forEach(async (item) => {
        if (item.items) {
          const newDataParcela: any = item.items.map((i) => ({
            nc: i.nc,
            linhagem: i.linhagem,
            experimento: i.experimento,
            ncc: i.ncc,
            tag_rfid: i.tag_id,
          }));

          if (newDataParcela) {
            const dataParc = await insertDataToSupabase(
              newDataParcela,
              "parcelas"
            );

            if (dataParc) {
              const newDataLoteOrdemColheita: any = item.items.map((i) => ({
                numero_lote_colheita: i.lote_colheita,
                importacoes_ordem_colheita_id: dataIOC[0].id,
              }));
              const listaCombinada = newDataLoteOrdemColheita.map(
                (item: any, index: any) => {
                  const parcela_id = dataParc[index].id; // Pega o id da primeira lista correspondente ao índice
                  return { ...item, parcela_id }; // Injeta o campo id no objeto da segunda lista
                }
              );

              const dataLotesOrdemColheita = await insertDataToSupabase(
                listaCombinada,
                "lotes_ordem_colheita"
              );
            }
          }
        }
      });
    }
  } catch (error) {
    console.error("Erro ao enviar dados para o Supabase:", error);
  }
}

export async function persistProcessingOrderToSupabase(
  items: ProcessingOrder[],
  fileName: string
) {
  try {
    const dataIOB = await insertDataToSupabase(
      [{ nome_arquivo: fileName }],
      "importacoes_ordens_beneficiamento"
    );

    if (dataIOB) {
      const agrupadosPorLote = items.reduce(
        (acc: { [key: string]: ProcessingOrder[] }, obj: ProcessingOrder) => {
          if (!acc[obj.lote]) {
            acc[obj.lote] = [];
          }
          acc[obj.lote].push(obj);
          return acc;
        },
        {}
      );

      const listaComIndices = Object.entries(agrupadosPorLote).map(
        ([lote, items]) => ({
          lote,
          items,
        })
      );

      //console.log(JSON.stringify(listaComIndices, null, 2));

      listaComIndices.forEach(async (item) => {
        if (item.items) {
          const newDataLote: any = [{ numero_lote: item.lote }];
          const nccList: any = item.items.map((i) => i.ncc);

          if (nccList) {
            const parcelas: any = await getDataFromSupabaseByField(
              "ncc",
              nccList,
              "parcelas"
            );

            if (parcelas && newDataLote) {
              const dataLote = await insertDataToSupabase(newDataLote, "lotes");

              if (dataLote) {
                const listaCombinada: any = item.items.map((itemOB: any) => {
                  const itemParcela = parcelas.find(
                    (iParcela: any) => iParcela.ncc === itemOB.ncc
                  );

                  if (itemParcela) {
                    return { ...itemOB, parcela_id: itemParcela.id };
                  }

                  return itemOB;
                });

                const newDataLoteOrdemBeneficiamento: any = listaCombinada.map(
                  (lb: any) => ({
                    importacao_ordem_beneficiamento_id: dataIOB[0].id,
                    lote_id: dataLote[0].id,
                    peso_parcela: lb.peso,
                    cultura: lb.cultura,
                    parcela_id: lb.parcela_id,
                  })
                );
                const ordensFiltradas: any[] =
                  newDataLoteOrdemBeneficiamento.filter(
                    (item: any) => item.parcela_id !== undefined
                  );

                if (ordensFiltradas.length > 0) {
                  const dataLotesOrdemBeneficiamento =
                    await insertDataToSupabase(
                      ordensFiltradas,
                      "lotes_ordem_beneficiamento"
                    );
                }
              }
            }
          }
        }
      });
    }
  } catch (error) {
    console.error("Erro ao enviar dados para o Supabase:", error);
  }
}

export async function persistProductionOrderToSupabase(
  items: ProductionOrder[],
  fileName: string
) {
  try {
    const dataIOP = await insertDataToSupabase(
      [{ nome_arquivo: fileName }],
      "importacoes_ordens_producao"
    );

    if (dataIOP) {
      //console.log(JSON.stringify(listaComIndices, null, 2));

      const nccList: any = items.map((i) => i.ncc);
      const parcelas: any = await getDataFromSupabaseByField(
        "ncc",
        nccList,
        "parcelas"
      );

      if (parcelas) {
        const newDataOrdemProducao: any = [
          {
            op: items[0].op,
            ensaio: items[0].ensaio,
          },
        ];

        if (newDataOrdemProducao) {
          const dataOP = await insertDataToSupabase(
            newDataOrdemProducao,
            "ordens_producao"
          );

          if (dataOP) {
            const listaCombinada: any = items.map((itemOP: any) => {
              const itemParcela = parcelas.find(
                (iParcela: any) => iParcela.ncc === itemOP.ncc
              );

              if (itemParcela) {
                return { ...itemOP, parcela_id: itemParcela.id };
              }

              return itemOP;
            });

            if (listaCombinada) {
              const newDataParcelaOrdemProducao: any = listaCombinada.map(
                (lp: any) => ({
                  importacoes_ordem_producao_id: dataIOP[0].id,
                  parcela_id: lp.parcela_id,
                  ordem_producao_id: dataOP[0].id,
                })
              );
              if (newDataParcelaOrdemProducao.length > 0) {
                const datanewDataParcelaOrdemProducao =
                  await insertDataToSupabase(
                    newDataParcelaOrdemProducao,
                    "parcelas_ordem_producao"
                  );
              }
            }
          }
        }
      }
    }
  } catch (error) {
    console.error("Erro ao enviar dados para o Supabase:", error);
  }
}

export async function persistEnvelopeContagemToSupabase(
  items: DocumentoEnvelopeContagem[],
  fileName: string
) {
  try {
    const dataIEC = await insertDataToSupabase(
      [{ nome_arquivo: fileName }],
      "importacoes_envelopes_contagem"
    );

    if (dataIEC) {
      //console.log(JSON.stringify(listaComIndices, null, 2));

      const nccList: any = items.map((i) => i.nca);
      const parcelas: any = await getDataFromSupabaseByField(
        "ncc",
        nccList,
        "parcelas"
      );

      const parcelasList: any = parcelas?.map((i: any) => i.id);
      const parcelas_ordem_producao: any = await getDataFromSupabaseByField(
        "parcela_id",
        parcelasList,
        "parcelas_ordem_producao"
      );

      if (parcelas && parcelas_ordem_producao) {
        const listaCombinada: any = items
          .map((itemEC: DocumentoEnvelopeContagem) => {
            const itemParcela = parcelas.find(
              (iParcela: any) => iParcela.ncc === itemEC.nca
            );

            if (itemParcela) {
              return { ...itemEC, parcela_id: itemParcela.id };
            }

            return null;
          })
          .filter((item: any) => item !== null);

        if (listaCombinada) {
          const listaCombinadaFinal: any = listaCombinada
            .map((itemLCF: any) => {
              const itemParcelaOrdemProducao = parcelas_ordem_producao.find(
                (iParcelaOrdemProducao: any) =>
                  iParcelaOrdemProducao.parcela_id === itemLCF.parcela_id
              );

              if (itemParcelaOrdemProducao) {
                return {
                  ...itemLCF,
                  parcela_ordem_producao_id: itemParcelaOrdemProducao.id,
                };
              }

              return null;
            })
            .filter((item: any) => item !== null);

          if (listaCombinadaFinal) {
            const newDataEnvelopeContagem: any = listaCombinadaFinal.map(
              (lcf: any) => ({
                parcela_ordem_producao_id: lcf.parcela_ordem_producao_id,
                numero_envelope: lcf.npe,
                concluido:
                  lcf.status === "S" ? true : lcf.status === "N" ? false : null,
                importacao_envelope_contagem_id: dataIEC[0].id,
                gli: lcf.gli,
                codlocal: lcf.codlocal,
                linhagem: lcf.linhagem,
              })
            );

            if (newDataEnvelopeContagem.length > 0) {
              const dataNewEnvelopeContagem = await upsertDataToSupabase(
                newDataEnvelopeContagem,
                "envelopes_contagem",
                ["parcela_ordem_producao_id", "numero_envelope"]
              );
            }
          }
        }
      }
    }
  } catch (error) {
    console.error("Erro ao enviar dados para o Supabase:", error);
  }
}

export async function persistCaixasExpedicaoToSupabase(
  items: DocumentoExpedicao[],
  fileName: string
) {
  try {
    const dataIE = await insertDataToSupabase(
      [{ nome_arquivo: fileName }],
      "importacoes_expedicoes"
    );

    if (dataIE) {
      //console.log(JSON.stringify(listaComIndices, null, 2));

      const newDataCaixasExpedicao: any = items.map((eci: any) => ({
        ensaio: eci.ensaio,
        local: eci.local,
        caixa: eci.caixa,
        tag_id: eci.tag_id,
        exped: eci.exped,
        importacao_expedicao_id: dataIE[0].id,
      }));

      if (newDataCaixasExpedicao.length > 0) {
        const dataNewCaixasExpedicao = await insertDataToSupabase(
          newDataCaixasExpedicao,
          "caixas_expedicao"
        );
      }
    }
  } catch (error) {
    console.error("Erro ao enviar dados para o Supabase:", error);
  }
}

async function insertDataToSupabase(items: any, tableName: string) {
  const supabase = createClient();
  try {
    const { data, error } = await supabase
      .from(tableName)
      .insert(items)
      .select();

    if (!error && data) {
      return data;
    }

    if (error) {
      console.error("Erro ao enviar dados para o Supabase:", error);
    }
  } catch (error) {
    console.error("Erro ao enviar dados para o Supabase:", error);
  }
}

async function upsertDataToSupabase(
  items: any[],
  tableName: string,
  uniqueFields: string[] // campos que identificam um registro único
) {
  console.log("DATA => ", items);
  const supabase = createClient();
  try {
    const { data, error } = await supabase
      .from(tableName)
      .upsert(items, {
        onConflict: uniqueFields.join(","), // campos que identificam duplicatas
        ignoreDuplicates: false, // false para atualizar registros existentes
      })
      .select();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error("Erro ao enviar dados para o Supabase:", error);
    throw error; // propagar erro para tratamento adequado
  }
}

async function getDataFromSupabaseByField(
  filterField: any,
  filterFieldValue: any,
  tableName: string
) {
  const supabase = createClient();
  try {
    // Verifica se filterFieldValue é um array e se é grande o suficiente para causar problemas
    if (Array.isArray(filterFieldValue) && filterFieldValue.length > 100) {
      // Processa em lotes de 100 itens para evitar URI muito grande
      const batchSize = 100;
      let allResults: any[] = [];

      // Divide o array em lotes menores
      for (let i = 0; i < filterFieldValue.length; i += batchSize) {
        const batch = filterFieldValue.slice(i, i + batchSize);

        const { data, error } = await supabase
          .from(tableName)
          .select()
          .in(filterField, batch);

        if (error) {
          continue;
        }

        if (data && data.length > 0) {
          allResults = [...allResults, ...data];
        }
      }

      return allResults;
    } else {
      // Comportamento original para arrays pequenos ou valores não-array
      const { data, error } = await supabase
        .from(tableName)
        .select()
        .in(filterField, filterFieldValue);

      if (error) {
        console.error("Erro na consulta ao Supabase:", error);
        return [];
      }

      if (data) {
        return data;
      }
    }

    return [];
  } catch (error) {
    console.error("Erro ao receber dados do Supabase:", error);
    return [];
  }
}

export async function getImportedFiles(
  fileName: string
): Promise<any[] | undefined> {
  const supabase = createClient();
  try {
    const { data, error } = await supabase.from(fileName).select(`
      id,
      nome_arquivo,
      created_at
    `);

    if (!error && data) {
      return data;
    }
  } catch (error) {
    console.error("Erro ao buscar dados para do Supabase:", error);
  }
}

export async function getPortionsByImportedFile(importedFileId: string) {
  const supabase = createClient();
  try {
    const { data, error } = await supabase
      .from("lotes_ordem_colheita")
      .select(
        `
      parcelas ( ncc, linhagem, experimento, tag_rfid ),
      numero_lote_colheita,
      importacoes_ordem_colheita_id,
      confirmado
    `
      )
      .eq("importacoes_ordem_colheita_id", importedFileId);

    if (!error && data) {
      const parcelas: OrdemColheita[] = data.map((item: any) => ({
        ncc: item.parcelas.ncc,
        linhagem: item.parcelas.linhagem,
        experimento: item.parcelas.experimento,
        tag_rfid: item.parcelas.tag_rfid,
        numero_lote_colheita: item.numero_lote_colheita,
        importacao_ordem_colheita_id: item.importacoes_ordem_colheita_id,
        confirmado: item.confirmado,
      }));

      return parcelas;
    }
  } catch (error) {
    console.error("Erro ao buscar dados para do Supabase:", error);
  }
}

export async function getImportedProcessingOrderFiles() {
  const supabase = createClient();
  try {
    const { data, error } = await supabase.from(
      "importacao_ordens_beneficiamento"
    ).select(`
      id,
      nome_arquivo,
      created_at
    `);

    if (!error && data) {
      return data;
    }
  } catch (error) {
    console.error("Erro ao buscar dados do Supabase:", error);
  }
}

export async function getPortionsByImportedProcessingOrderFile(
  importedFileId: string
) {
  const supabase = createClient();
  try {
    const { data, error } = await supabase
      .from("lotes_ordem_beneficiamento")
      .select(
        `
      parcelas ( ncc, linhagem, tag_rfid ),
      peso_parcela,
      cultura,
      importacao_ordem_beneficiamento_id,
      loles ( numero_lote )
    `
      )
      .eq("importacao_ordem_beneficiamento_id", importedFileId);

    if (!error && data) {
      const parcelas: OrdemBeneficiamento[] = data.map((item: any) => ({
        ncc: item.parcelas.ncc,
        linhagem: item.parcelas.linhagem,
        tag_rfid: item.parcelas.tag_rfid,
        numero_lote: item.lotes.numero_lote,
        importacao_ordem_beneficiamento_id:
          item.importacao_ordem_beneficiamento_id,
        peso_parcela: item.peso_parcela,
        cultura: item.cultura,
      }));

      return parcelas;
    }
  } catch (error) {
    console.error("Erro ao buscar dados do Supabase:", error);
  }
}

export async function persistWeightUpdateToSupabase(
  items: ProcessingOrder[],
  fileName: string
) {
  try {
    const dataIOB = await insertDataToSupabase(
      [{ nome_arquivo: fileName }],
      "importacoes_ordens_beneficiamento"
    );

    if (dataIOB) {
      const agrupadosPorLote = items.reduce(
        (acc: { [key: string]: ProcessingOrder[] }, obj: ProcessingOrder) => {
          if (!acc[obj.lote]) {
            acc[obj.lote] = [];
          }
          acc[obj.lote].push(obj);
          return acc;
        },
        {}
      );

      const listaComIndices = Object.entries(agrupadosPorLote).map(
        ([lote, items]) => ({
          lote,
          items,
        })
      );

      listaComIndices.forEach(async (item) => {
        if (item.items) {
          const newDataLote: any = [{ numero_lote: item.lote }];
          const nccList: any = item.items.map((i) => i.ncc);

          if (nccList) {
            const parcelas: any = await getDataFromSupabaseByField(
              "ncc",
              nccList,
              "parcelas"
            );

            if (parcelas && newDataLote) {
              const existingLote = await getDataFromSupabaseByField(
                "numero_lote",
                [item.lote],
                "lotes"
              );

              const dataLote =
                existingLote.length > 0
                  ? existingLote
                  : await insertDataToSupabase(newDataLote, "lotes");

              if (dataLote) {
                const listaCombinada: any = item.items.map((itemOB: any) => {
                  const itemParcela = parcelas.find(
                    (iParcela: any) => iParcela.ncc === itemOB.ncc
                  );

                  if (itemParcela) {
                    return { ...itemOB, parcela_id: itemParcela.id };
                  }

                  return itemOB;
                });

                const newDataLoteOrdemBeneficiamento: any = listaCombinada.map(
                  (lb: any) => ({
                    importacao_ordem_beneficiamento_id: dataIOB[0].id,
                    lote_id: dataLote[0].id,
                    peso_parcela: lb.peso,
                    cultura: lb.cultura,
                    parcela_id: lb.parcela_id,
                  })
                );

                // Busca registros existentes
                const existingRecords = await getDataFromSupabaseByField(
                  "lote_id",
                  [dataLote[0].id],
                  "lotes_ordem_beneficiamento"
                );

                const ordensFiltradas: any[] =
                  newDataLoteOrdemBeneficiamento.filter(
                    (item: any) => item.parcela_id !== undefined
                  );

                // Filtra apenas registros que realmente precisam ser atualizados
                const registrosParaInserir = ordensFiltradas.filter(
                  (novoRegistro: any) => {
                    // Procura registro existente
                    const registroExistente = existingRecords?.find(
                      (existing: any) =>
                        existing.parcela_id === novoRegistro.parcela_id &&
                        existing.lote_id === novoRegistro.lote_id &&
                        existing.peso_parcela === novoRegistro.peso_parcela
                    );

                    // Se não existe registro ou se existe mas com peso diferente, inclui para inserção
                    return !registroExistente;
                  }
                );

                if (registrosParaInserir.length > 0) {
                  const resultado = await insertDataToSupabase(
                    registrosParaInserir,
                    "lotes_ordem_beneficiamento"
                  );
                }
              }
            }
          }
        }
      });
    }
  } catch (error) {
    console.error("Erro ao enviar dados para o Supabase:", error);
  }
}
