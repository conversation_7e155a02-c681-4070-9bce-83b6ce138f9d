import { getImportedFiles } from './files-service';

function formatDate(createdAt: string): string {
  const date = new Date(createdAt);
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  }).format(date);
}

export async function getProcessingFilesUploaded() {
  const importedFilesList = await getImportedFiles(
    'importacoes_ordens_beneficiamento'
  );

  importedFilesList?.map((file) => {
    file.created_at = formatDate(file.created_at);
  });

  return importedFilesList ?? [];
} 