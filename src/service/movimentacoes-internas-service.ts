'use server'

import { createClient } from "../../utils/supabase/server";

export async function newMovimentacaoInterna(desalocacaoId: string) {
  const supabase = createClient()

  const { data, error } = await supabase
    .from('movimentacoes_internas')
    .insert({desalocacao_id: desalocacaoId})
    .select();

  if (!error && data) {
    return data[0].id;
  } else {
    throw new Error(error.message);
  }
}

export async function updateMovimentacaoInterna(movimentacaoInternaId: string, newAlocacaoId: string) {
  const supabase = createClient() 

  const { data, error } = await supabase
    .from('movimentacoes_internas')
    .update({alocacao_id: newAlocacaoId})
    .eq('id', movimentacaoInternaId)
    .select()

  if (!error && data) {
    return "Dados Inseridos com sucesso!"
  } else {
    throw new Error(error.message)
  }
}