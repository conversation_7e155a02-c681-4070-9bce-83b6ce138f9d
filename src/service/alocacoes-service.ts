"use server";

import {
  Alocacao,
  AlocacaoItem,
  Rua,
  Baia,
  Bloco,
  AlocacaoListItem,
} from "@/data/model/Alocacao";
import { createClient } from "../../utils/supabase/server";
import { Lote, LoteMovimentacaoInterna } from "@/data/model/Lote";

export async function getAlocacoesList(
  page: number,
  pageSize: number,
  search: string
): Promise<{ data: AlocacaoListItem[]; count: number } | undefined> {
  const supabase = createClient();

  const from = (page - 1) * pageSize; // 0-based index for Supabase range
  const to = from + pageSize - 1;

  let query = supabase.from("vw_alocacoes_list").select(
    `
        id,
        created_at,
        numero_controle,
        user_email,
        rua,
        bloco,
        baia
      `,
    { count: "exact" }
  );

  if (search) {
    // Implementar lógica de filtro no Supabase para outros campos se necessário
    query = query.or(
      `numero_controle.ilike.%${search}%,created_at_str.ilike.%${search}%,user_email.ilike.%${search}%,rua.ilike.%${search}%,bloco.ilike.%${search}%,baia.ilike.%${search}%`
    );
  }

  const { data, error, count } = await query.range(from, to);

  if (!error && data) {
    const alocacoes: AlocacaoListItem[] = data.map((item: any) => ({
      id: item.id,
      created_at: item.created_at,
      numero_controle: item.numero_controle,
      user_email: item.user_email,
      rua: item.rua,
      bloco: item.bloco,
      baia: item.baia,
    }));

    return { data: alocacoes, count: count || 0 };
  } else {
    throw new Error(error.message);
  }
}

export async function getLotesEmEstoque(): Promise<any[]> {
  const supabase = createClient();
  const { data, error } = await supabase
    .from("vw_lotes_em_estoque")
    .select("*");
  if (!error && data) {
    return data;
  } else {
    throw new Error(error?.message || "Erro ao buscar lotes em estoque");
  }
}

/**
 * Busca o número do lote correspondente ao NCC informado na view vw_parcelas_estoque_planta_baixa.
 * @param ncc NCC a ser buscado
 * @returns numero_lote correspondente ou undefined se não encontrado
 */
export async function getNumeroLotePorNCC(
  ncc: string
): Promise<string | undefined> {
  const supabase = createClient();
  const { data, error } = await supabase
    .from("vw_parcelas_estoque_planta_baixa")
    .select("numero_lote")
    .eq("ncc", ncc)
    .limit(1)
    .single();

  if (!error && data && data.numero_lote) {
    return data.numero_lote;
  }
  return undefined;
}

/**
 * Busca todos os lotes ocupando endereços (vw_lotes_em_estoque_completo)
 */
export async function getLotesAlocadosEmEstoque(): Promise<any[]> {
  const supabase = createClient();
  const { data, error } = await supabase
    .from("vw_lotes_estoque_planta_baixa")
    .select("*");
  if (!error && data) {
    return data;
  } else {
    throw new Error(
      error?.message || "Erro ao buscar lotes alocados em estoque completo"
    );
  }
}

export async function getAlocacaoById(
  id: string
): Promise<Alocacao | undefined> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from("alocacoes")
    .select(
      `
        id,
        created_at,
        numero_controle,
        rua_id,
        bloco_id,
        baia_id,
        user_profiles ( email ),
        ruas ( nome ),
        blocos ( nome ),
        baias ( nome ),
        itens_alocacao (
          lotes (
            id,
            numero_lote
          )
        )
      `
    )
    .eq("id", id)
    .is("ativo", true);

  if (!error && data) {
    const alocacoes: Alocacao[] = data.map((item: any) => ({
      id: item.id,
      created_at: item.created_at,
      numero_controle: item.numero_controle,
      rua_id: item.rua_id,
      bloco_id: item.bloco_id,
      baia_id: item.baia_id,
      user_email: item.user_profiles.email,
      rua: item.ruas.nome,
      bloco: item.blocos.nome,
      baia: item.baias.nome,
      lotes:
        item.itens_alocacao?.map((itemAlocacao: any) => ({
          id: itemAlocacao.lotes.id,
          numero_lote: itemAlocacao.lotes.numero_lote,
        })) || [],
    }));

    return alocacoes[0];
  } else {
    throw new Error(error.message);
  }
}

export async function getItemsAlocacaoByAlocacaoId(
  alocacaoId: string
): Promise<AlocacaoItem[] | undefined> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from("itens_alocacao")
    .select(
      `
        id,
        alocacoes ( numero_controle, ruas (nome), blocos (nome), baias (nome) ),
        lotes ( numero_lote )
      `
    )
    .eq("alocacao_id", alocacaoId)
    .is("ativo", true);

  if (!error && data) {
    const itensAlocacao: AlocacaoItem[] = data.map((item: any) => ({
      id: item.id,
      alocacao: item.alocacoes.numero_controle,
      lote: item.lotes.numero_lote,
      rua: item.alocacoes.ruas.nome,
      bloco: item.alocacoes.blocos.nome,
      baia: item.alocacoes.baias.nome,
    }));

    return itensAlocacao;
  } else {
    throw new Error(error.message);
  }
}

export async function getRuas(): Promise<Rua[] | undefined> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from("ruas")
    .select("*")
    .order("nome", { ascending: true });
  if (!error && data) {
    return data;
  } else {
    throw new Error(error.message);
  }
}

export async function getRuasById(id: string): Promise<any | undefined> {
  const supabase = createClient();

  const { data, error } = await supabase.from("ruas").select("*").eq("id", id);
  if (!error && data) {
    return data[0];
  } else {
    throw new Error(error.message);
  }
}

export async function insertRua(rua: any): Promise<Rua | undefined> {
  const supabase = createClient();

  const { data, error } = await supabase.from("ruas").insert(rua).select();
  if (!error && data) {
    return data[0];
  } else {
    throw new Error(error.message);
  }
}

export async function updateRua(rua: any): Promise<Rua | undefined> {
  const supabase = createClient();

  const dataUpdate = { nome: rua.nome };

  const { data, error } = await supabase
    .from("ruas")
    .update(dataUpdate)
    .eq("id", rua.id)
    .select();
  if (!error && data) {
    return data[0];
  } else {
    throw new Error(error.message);
  }
}

export async function deleteRua(ruaId: string): Promise<Rua | undefined> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from("ruas")
    .delete()
    .eq("id", ruaId)
    .select();
  if (!error && data) {
    return data[0];
  } else {
    throw new Error(error.message);
  }
}

export async function getBlocos(): Promise<Bloco[] | undefined> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from("blocos")
    .select("*")
    .order("nome", { ascending: true });
  if (!error && data) {
    return data;
  } else {
    throw new Error(error.message);
  }
}

export async function getBlocosById(id: string): Promise<any | undefined> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from("blocos")
    .select("*")
    .eq("id", id);
  if (!error && data) {
    return data[0];
  } else {
    throw new Error(error.message);
  }
}

export async function insertBloco(bloco: any): Promise<Bloco | undefined> {
  const supabase = createClient();

  const { data, error } = await supabase.from("blocos").insert(bloco).select();
  if (!error && data) {
    return data[0];
  } else {
    throw new Error(error.message);
  }
}

export async function updateBloco(bloco: any): Promise<Bloco | undefined> {
  const supabase = createClient();

  const dataUpdate = { nome: bloco.nome };

  const { data, error } = await supabase
    .from("blocos")
    .update(dataUpdate)
    .eq("id", bloco.id)
    .select();
  if (!error && data) {
    return data[0];
  } else {
    throw new Error(error.message);
  }
}

export async function deleteBloco(blocoId: string): Promise<Bloco | undefined> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from("blocos")
    .delete()
    .eq("id", blocoId)
    .select();
  if (!error && data) {
    return data[0];
  } else {
    throw new Error(error.message);
  }
}

export async function getBaias(): Promise<Baia[] | undefined> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from("baias")
    .select("*")
    .order("nome", { ascending: true });
  if (!error && data) {
    return data;
  } else {
    throw new Error(error.message);
  }
}

export async function getBaiasById(id: string): Promise<any | undefined> {
  const supabase = createClient();

  const { data, error } = await supabase.from("baias").select("*").eq("id", id);
  if (!error && data) {
    return data[0];
  } else {
    throw new Error(error.message);
  }
}

export async function insertBaia(baia: any): Promise<Baia | undefined> {
  const supabase = createClient();

  const { data, error } = await supabase.from("baias").insert(baia).select();
  if (!error && data) {
    return data[0];
  } else {
    throw new Error(error.message);
  }
}

export async function updateBaia(baia: any): Promise<Baia | undefined> {
  const supabase = createClient();

  const dataUpdate = { nome: baia.nome };

  const { data, error } = await supabase
    .from("baias")
    .update(dataUpdate)
    .eq("id", baia.id)
    .select();
  if (!error && data) {
    return data[0];
  } else {
    throw new Error(error.message);
  }
}

export async function deleteBaia(baiaId: string): Promise<Baia | undefined> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from("baias")
    .delete()
    .eq("id", baiaId)
    .select();
  if (!error && data) {
    return data[0];
  } else {
    throw new Error(error.message);
  }
}

export async function getLotesDesalocados(): Promise<Lote[] | undefined> {
  const supabase = createClient();

  const { data, error } = await supabase
    //.from('vw_lotes_desalocados')
    .from("vw_lotes_para_alocacao_externa")
    .select("*");
  if (!error && data) {
    return data;
  } else {
    throw new Error(error.message);
  }
}

export async function getLotesAlocados(): Promise<Lote[] | undefined> {
  const supabase = createClient();

  //const { data, error } = await supabase.from('vw_lotes_alocados').select('*');
  const { data, error } = await supabase
    .from("vw_lotes_para_alocacao_externa")
    .select("*");
  if (!error && data) {
    return data;
  } else {
    throw new Error(error.message);
  }
}

export async function getLotesParaAlocacaoInterna() {
  const supabase = createClient();

  const { data, error } = await supabase
    .from("vw_lotes_para_alocacao_interna")
    .select()
    .is("ativo", true);

  if (!error && data) {
    const lotes: LoteMovimentacaoInterna[] = data.map((item: any) => ({
      id: item.id,
      created_at: item.created_at,
      updated_at: item.updated_at,
      ativo: item.ativo,
      numero_lote: item.numero_lote,
      movimentacao_interna_id: item.movimentacao_interna_id,
    }));

    return lotes;
  } else {
    throw new Error(error.message);
  }
}

export async function getLotesParaAlocacaoEterna() {
  const supabase = createClient();

  const { data, error } = await supabase
    .from("vw_lotes_para_alocacao_externa")
    .select()
    .is("ativo", true);

  if (!error && data) {
    const lotes: Lote[] = data.map((item: any) => ({
      id: item.id,
      created_at: item.created_at,
      updated_at: item.updated_at,
      ativo: item.ativo,
      numero_lote: item.numero_lote,
    }));

    return lotes;
  } else {
    throw new Error(error.message);
  }
}
