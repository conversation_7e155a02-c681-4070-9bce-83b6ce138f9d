import type { <PERSON>ada<PERSON> } from 'next';
import './globals.css';
import { Montserrat } from 'next/font/google';
import { Toaster } from '@/components/ui/toaster';

export const metadata: Metadata = {
  title: 'Had<PERSON>',
  description: 'Gestor de Processos',
};

const font = Montserrat({
  subsets: ['latin'],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang='pt-BR'>
      <body className={font.className}>
        {children}
        <Toaster />
      </body>
    </html>
  );
}
