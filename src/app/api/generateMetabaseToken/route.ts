// src/app/api/generateMetabaseToken/route.ts
import { NextResponse } from "next/server";
import jwt from "jsonwebtoken";

const METABASE_SITE_URL = process.env.NEXT_PUBLIC_METABASE_SITE_URL || "https://bi.lrsolucoes.dev.br";
const METABASE_SECRET_KEY = process.env.METABASE_SECRET_KEY || "";

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const dashboardId = searchParams.get("dashboardId");

  if (!dashboardId) {
    return NextResponse.json({ error: "Dashboard ID is required" }, { status: 400 });
  }

  const payload = {
    resource: { dashboard: Number(dashboardId) },
    params: {},
    exp: Math.floor(Date.now() / 1000) + 10 * 60, // Expira em 10 minutos
  };

  try {
    const token = jwt.sign(payload, METABASE_SECRET_KEY);
    const iframeUrl = `${METABASE_SITE_URL}/embed/dashboard/${token}#bordered=false&titled=true`;

    return NextResponse.json({ iframeUrl });
  } catch (error) {
    return NextResponse.json({ error: "Failed to generate token" }, { status: 500 });
  }
}