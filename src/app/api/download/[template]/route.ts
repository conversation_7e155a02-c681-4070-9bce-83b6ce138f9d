import { NextRequest, NextResponse } from 'next/server'
import path from 'path'
import fs from 'fs'

export async function GET(
  request: NextRequest,
  { params }: { params: { template: string } }
) {
  try {
    const templateName = params.template
    const templatesDir = path.join(process.cwd(), 'public', 'templates')
    
    const templateMap = {
      'harvest-template': 'ordem-colheita-template.xlsx',
      'processing-template': 'ordem-beneficiamento-template.xlsx',
      'production-template': 'ordem-producao-template.xlsx',
      'envelope-template': 'envelope-contagem-template.xlsx',
      'expedicao-template': 'caixa-expedicao-template.xlsx'
    }

    const filename = templateMap[templateName as keyof typeof templateMap]
    if (!filename) {
      return NextResponse.json(
        { error: 'Template não encontrado' },
        { status: 404 }
      )
    }

    const filePath = path.join(templatesDir, filename)
    if (!fs.existsSync(filePath)) {
      return NextResponse.json(
        { error: 'Arquivo de template não encontrado' },
        { status: 404 }
      )
    }

    const fileBuffer = fs.readFileSync(filePath)
    const response = new NextResponse(fileBuffer)

    response.headers.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response.headers.set('Content-Disposition', `attachment; filename=${filename}`)

    return response
  } catch (error) {
    console.error('Erro ao baixar template:', error)
    return NextResponse.json(
      { error: 'Erro ao processar o download do template' },
      { status: 500 }
    )
  }
} 