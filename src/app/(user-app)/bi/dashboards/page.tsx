import MetabaseDashboardEmbed from '@/components/metabase-dashboard-embed';
import { Metadata } from 'next';
import React from 'react';

export const metadata: Metadata = {
  title: 'BI Dashboards',
  description: 'Dashboards de Business Intelligence',
};

function BiDashboardsPage() {
  const dashboardId = Number(process.env.NEXT_PUBLIC_DASHBOARD_BI_ID);
  return (
    <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
      <div className='hidden h-full flex-1 flex-col space-y-8 p-8 md:flex'>
        <div className='flex items-center justify-between space-y-2'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>
              Dashboard de Business Intelligence
            </h2>
            <p className='text-muted-foreground'>
              Visualize os indicadores e métricas do negócio
            </p>
          </div>
        </div>
        {dashboardId ? <MetabaseDashboardEmbed dashboardId={dashboardId} /> : <p>ID do Dashboard não informado.</p>}
      </div>
    </div>
  );
}

export default BiDashboardsPage;
