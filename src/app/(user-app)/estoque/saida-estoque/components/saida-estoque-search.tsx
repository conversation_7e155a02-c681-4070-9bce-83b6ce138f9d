"use client";

import * as React from "react";
import { Search } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { toast } from "@/hooks/use-toast";
import { createClient } from "../../../../../../utils/supabase/client";

interface OrdemProducao {
  id: string;
  op: string;
  created_at: string;
}

interface SaidaEstoqueSearchProps {
  selectedOP: string;
  onSelectOP: (op: string) => void;
}

export function SaidaEstoqueSearch(
  { selectedOP, onSelectOP }: SaidaEstoqueSearchProps,
) {
  const supabase = createClient();
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);
  const [ordensProducao, setOrdensProducao] = React.useState<OrdemProducao[]>(
    [],
  );
  const [searchQuery, setSearchQuery] = React.useState("");

  const handleSearch = async () => {
    try {
      const { data, error } = await supabase
        .from("ordens_producao")
        .select("*")
        .ilike("op", `%${searchQuery}%`)
        .order("created_at", { ascending: false });

      if (error) throw error;
      setOrdensProducao(data || []);
    } catch (error) {
      console.error("Error fetching ordens_producao:", error);
      toast({
        variant: "destructive",
        title: "Erro",
        description: "Erro ao buscar ordens de produção",
      });
    }
  };

  const handleSelectOP = (op: string) => {
    onSelectOP(op);
    setIsDialogOpen(false);
  };

  return (
    <div className="flex gap-2 items-center">
      <Input
        value={selectedOP}
        placeholder="Número da OP"
        className="max-w-[200px]"
        readOnly
      />
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" size="icon">
            <Search className="h-4 w-4" />
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Buscar Ordem de Produção</DialogTitle>
          </DialogHeader>
          <div className="flex gap-2 mb-4">
            <Input
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Digite o número da OP"
            />
            <Button onClick={handleSearch}>Buscar</Button>
          </div>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>OP</TableHead>
                  <TableHead>Data Criação</TableHead>
                  <TableHead>Ação</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {ordensProducao.map((op) => (
                  <TableRow key={op.id}>
                    <TableCell>{op.op}</TableCell>
                    <TableCell>
                      {new Date(op.created_at).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSelectOP(op.op)}
                      >
                        Selecionar
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
                {ordensProducao.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={3} className="text-center">
                      Nenhum resultado encontrado
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
