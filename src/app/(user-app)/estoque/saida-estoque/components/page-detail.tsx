"use client";

import { useCallback, useEffect, useState } from "react";
import { columns } from "./columns";
import { DataTable } from "@/components/data-table/data-table-ref";
import { Skeleton } from "@/components/ui/skeleton";
import { createClient } from "../../../../../../utils/supabase/client";
import { SaidaEstoqueList } from "@/data/model/SaidaEstoque";
import { useSearchParams, useRouter } from "next/navigation";

export default function SaidaEstoquePageDetail() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const page = parseInt(searchParams.get("page") || "1");
  const pageSize = parseInt(searchParams.get("pageSize") || "10");
  const search = searchParams.get("search") || "";

  const supabase = createClient();
  const [registros, setRegistros] = useState<SaidaEstoqueList[]>([]);
  const [isLoading, setIsLoading] = useState<Boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  useEffect(() => {
    getSaidaEstoque(search);
  }, [page, pageSize, search]);

  const getSaidaEstoque = useCallback(
    async (filterValue: string = "") => {
      setIsLoading(true);
      setError(null);
      const from = (page - 1) * pageSize; // 0-based index for Supabase range
      const to = from + pageSize - 1;

      let query = supabase.from("vw_saidas_estoque_list").select(
        `
        id,
        created_at,
        numero_controle,
        user_email,
        op
        `,
        { count: "exact" }
      );

      if (filterValue) {
        query = query.or(
          `numero_controle.ilike.%${filterValue}%,created_at_str.ilike.%${filterValue}%,user_email.ilike.%${filterValue}%,op.ilike.%${filterValue}%`
        );
      }

      try {
        const {
          data: saidasData,
          error: saidasError,
          count,
        } = await query.range(from, to);

        if (saidasError) throw saidasError;

        if (!saidasData) {
          setRegistros([]);
          setTotalCount(0);
          return;
        }

        setRegistros(saidasData);
        setTotalCount(count || 0);
      } catch (err: any) {
        console.error("Erro ao buscar dados:", err);
        setError(err.message);
        setRegistros([]);
        setTotalCount(0);
      } finally {
        setIsLoading(false);
      }
    },
    [page, pageSize, search]
  );

  const onSearchChange = (value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    if (value && value !== "") {
      if (value !== search) {
        params.set("page", "1");
      }
    }
    params.set("search", value);
    router.push(`?${params.toString()}`);
  };

  const onPaginationChange = (newPage: number, newPageSize: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("page", newPage.toString());
    params.set("pageSize", newPageSize.toString());
    router.push(`?${params.toString()}`);
  };

  if (error) {
    return (
      <div className="text-red-500 p-4">Erro ao carregar dados: {error}</div>
    );
  }

  return (
    <>
      {isLoading ? (
        <Skeleton className="h-2/3 rounded-sm" />
      ) : (
        <DataTable
          showToolbar={true}
          data={registros}
          columns={columns}
          page={page}
          pageSize={pageSize}
          totalCount={totalCount}
          onPaginationChange={onPaginationChange}
          onSearchChange={onSearchChange}
          search={search}
        />
      )}
    </>
  );
}
