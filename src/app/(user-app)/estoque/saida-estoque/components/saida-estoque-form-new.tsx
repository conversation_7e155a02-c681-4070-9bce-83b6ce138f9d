"use client";

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Lote } from "@/data/model/Lote";
import { toast } from "@/hooks/use-toast";
import { DataTablePagination } from "./data-table-pagination";
import { useRouter } from "next/navigation";
import { createClient } from "../../../../../../utils/supabase/client";
import { SaidaEstoqueSearch } from "./saida-estoque-search";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  ordemProducaoId: string;
  onOPSelect?: (op: string) => void;
}

export function SaidaEstoqueFormNew<TData, TValue>({
  columns,
  data,
  ordemProducaoId,
  onOPSelect,
}: DataTableProps<TData, TValue>) {
  const supabase = createClient();
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] = React.useState<
    VisibilityState
  >({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [saveDisabled, setSaveDisabled] = React.useState(false);
  const [selectedOP, setSelectedOP] = React.useState("");
  const router = useRouter();

  const handleOPSelect = (op: string) => {
    setSelectedOP(op);
    onOPSelect?.(op);
  };

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  async function getSaidaEstoqueId(): Promise<string | null> {
    try {
      const { data: dataOP, error: errorOP } = await supabase
        .from("ordens_producao")
        .select()
        .eq("op", ordemProducaoId);

      if (!errorOP && dataOP) {
        const { data, error } = await supabase
          .from("saidas_estoque")
          .insert({ ordem_producao_id: dataOP[0].id })
          .select();

        if (!error && data) {
          return data[0].id;
        }
      }
    } catch (error) {
      console.error("Erro ao inserir Saida de Estoque no Supabase:", error);
    }

    return null;
  }

  async function insertSaidaEstoque(itemsSaidaEstoque: any[]) {
    try {
      const { data, error } = await supabase
        .from("itens_saida_estoque")
        .insert(itemsSaidaEstoque)
        .select();

      if (!error && data) {
        toast({
          duration: 3000,
          title: "Saida de Estoque",
          description: <p>Saida de Estoque gravada com sucesso!</p>,
        });
        setSaveDisabled(false);
        router.push("/estoque/saida-estoque");
      }
    } catch (error) {
      setSaveDisabled(false);
      console.error(
        "Erro ao inserir Itens de Saida de Estoque para no Supabase:",
        error,
      );
    }
  }

  async function handleSave() {
    const listSaidaEstoqueItem: any[] = [];
    const idSaidaEstoque = await getSaidaEstoqueId();

    if (table.getSelectedRowModel().rows.length <= 0) {
      toast({
        variant: "destructive",
        duration: 2000,
        title: "Alerta: Saída de Estoque",
        description: <p>Você precisa selecionar pelo menos um lote!</p>,
      });
      return;
    }

    setSaveDisabled(true);

    const lotes = table.getSelectedRowModel().rows.map((row) => {
      const lote = row.original as Lote;
      listSaidaEstoqueItem.push({
        lote_id: lote.id,
        saida_estoque_id: idSaidaEstoque,
      });
      return lote.id;
    });

    await insertSaidaEstoque(listSaidaEstoqueItem);
  }

  return (
    <div className="space-y-4">
      <div className="mb-4">
        <SaidaEstoqueSearch
          selectedOP={selectedOP}
          onSelectOP={handleOPSelect}
        />
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} colSpan={header.colSpan}>
                      {header.isPlaceholder ? null : flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length
              ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              )
              : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    Nenhum resultado.
                  </TableCell>
                </TableRow>
              )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination table={table} />

      <Button onClick={handleSave} variant="outline" disabled={saveDisabled}>
        Salvar
      </Button>
    </div>
  );
}
