"use client";

import React, { useState } from "react";
import { Said<PERSON>toqueFormNew } from "./saida-estoque-form-new";
import { columnsLote } from "./columns-lote";
import { Skeleton } from "@/components/ui/skeleton";

import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { createClient } from "../../../../../../utils/supabase/client";

const saidaEstoqueFormSchema = z.object({
  numero_ordem_producao: z
    .string({
      required_error: "Informe um número de Ordem de Produção.",
    })
    .min(4, {
      message: "Ordem de Produção precisa ter no mínimo 4 caracteres.",
    }),
});

type SaidaEstoqueFormValues = z.infer<typeof saidaEstoqueFormSchema>;

export default function SaidaEstoqueNew() {
  const supabase = createClient();
  const [registros, setRegistros] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const form = useForm<SaidaEstoqueFormValues>({
    resolver: zodResolver(saidaEstoqueFormSchema),
    defaultValues: { numero_ordem_producao: "" },
    mode: "onChange",
  });

  async function onSubmit(data: SaidaEstoqueFormValues) {
    setIsLoading(true);
    loadLotesOrdemProducao(data.numero_ordem_producao);
  }

  async function loadLotesOrdemProducao(op: string) {
    try {
      const { data, error } = await supabase.rpc(
        "get_lotes_disponiveis_saida",
        { numero_op: op },
      );

      if (!error && data) {
        setRegistros(data);
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Erro ao buscar dados no Supabase:", error);
    }
  }

  const handleOPSelect = (op: string) => {
    form.setValue("numero_ordem_producao", op);
    loadLotesOrdemProducao(op);
  };

  return (
    <>
      {isLoading ? <Skeleton className="h-72 rounded-sm" /> : (
        <div>
          <SaidaEstoqueFormNew
            ordemProducaoId={form.getValues().numero_ordem_producao}
            data={registros}
            columns={columnsLote}
            onOPSelect={handleOPSelect}
          />
        </div>
      )}
    </>
  );
}
