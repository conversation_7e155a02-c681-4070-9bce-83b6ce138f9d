import { createClient } from '../../../../../../utils/supabase/server';
import { SaidaEstoqueFormShow } from '../components/saida-estoque-form-show';
import <PERSON><PERSON><PERSON>queNew from '../components/saida-estoque-new';

async function getSaidaEstoque(saida_id: string): Promise<any | null> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('saidas_estoque')
      .select(
        `id, created_at, numero_controle, user_profiles (email), ordens_producao (op)`
      )
      .eq('id', saida_id);

    if (!error && data) {
      // retira o objeto aninhado
      const formattedData = data.map((item: any) => ({
        ...item,
        email: item.user_profiles.email,
        op: item.ordens_producao.op,
      }));

      // Remover o campo `user_profiles` agora desnecessário
      formattedData.forEach((item) => {
        delete item.user_profiles;
        delete item.ordens_producao;
      });

      return formattedData[0];
    }
  } catch (error) {
    console.error('Erro ao buscar dados para do Supabase:', error);
  }

  return null;
}

async function getSaidaEstoqueItens(saida_id: string): Promise<any[]> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('itens_saida_estoque')
      .select(`id, lotes (numero_lote)`)
      .eq('saida_estoque_id', saida_id)
      .eq('ativo', true);

    if (!error && data) {
      // retira o objeto aninhado
      const formattedData = data.map((item: any) => ({
        ...item,
        numero_lote: item.lotes.numero_lote,
      }));

      // Remover o campo `lotes` agora desnecessário
      formattedData.forEach((item) => delete item.lotes);

      return formattedData;
    }
  } catch (error) {
    console.error('Erro ao buscar dados para do Supabase:', error);
  }

  return [];
}

export default async function SaidaEstoqueEditPage({
  params,
}: {
  params: any;
}) {
  const { id } = params;
  const saidaEstoque = await getSaidaEstoque(id);
  const itensSaidaEstoque = await getSaidaEstoqueItens(id);

  return (
    <div className='container mx-auto py-4'>
      <h1 className='text-2xl font-bold mb-4'>
        {id === 'new'
          ? 'Nova Saida de Estoque'
          : `Consultar Saida de Estoque: ${saidaEstoque?.numero_controle}`}
      </h1>
      {id === 'new' ? (
        <SaidaEstoqueNew />
      ) : (
        <SaidaEstoqueFormShow
          itemsSaidaEstoque={itensSaidaEstoque}
          initialData={saidaEstoque}
        />
      )}
    </div>
  );
}
