"use client";

import React, { useEffect, useState } from "react";
import PlantaEstoque from "@/components/planta-estoque/PlantaEstoque";
import { EnderecoEstoque } from "@/components/planta-estoque/types";
import {
  getRuas,
  getBlocos,
  getBaias,
  getLotesAlocadosEmEstoque,
  getNumeroLotePorNCC,
} from "@/service/alocacoes-service";
import { useDebounce } from "@/hooks/use-debounce";
import { Button } from "@/components/ui/button";
import { useSidebar } from "@/components/ui/sidebar";
import { FilterIcon } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

export default function PlantaEstoquePage() {
  const { state } = useSidebar();
  const [enderecos, setEnderecos] = useState<EnderecoEstoque[]>([]);
  const [loading, setLoading] = useState(true);
  const [filtersOpen, setFiltersOpen] = useState(true);

  // Estados para filtros
  const [filtroNCC, setFiltroNCC] = useState("");
  const filtroNCCDebounced = useDebounce(filtroNCC, 400);
  const [filtroNumeroLote, setFiltroNumeroLote] = useState("");
  const [filtroRua, setFiltroRua] = useState<string | undefined>(undefined);
  const [filtroBloco, setFiltroBloco] = useState<string | undefined>(undefined);
  const [filtroBaia, setFiltroBaia] = useState<string | undefined>(undefined);
  const [filtroStatus, setFiltroStatus] = useState<
    "todos" | "livre" | "ocupado"
  >("todos");

  // Verifica se há algum filtro ativo
  const hasActiveFilters = () => {
    return (
      filtroNumeroLote !== "" ||
      filtroRua !== undefined ||
      filtroBloco !== undefined ||
      filtroBaia !== undefined ||
      filtroNCC !== "" ||
      filtroStatus !== "todos"
    );
  };

  // Conta quantos filtros estão ativos
  const countActiveFilters = () => {
    let count = 0;
    if (filtroNumeroLote !== "") count++;
    if (filtroRua !== undefined) count++;
    if (filtroBloco !== undefined) count++;
    if (filtroBaia !== undefined) count++;
    if (filtroNCC !== "") count++;
    if (filtroStatus !== "todos") count++;
    return count;
  };

  // Estados para opções dos selects
  const [ruas, setRuas] = useState<any[]>([]);
  const [blocos, setBlocos] = useState<any[]>([]);
  const [baias, setBaias] = useState<any[]>([]);

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      try {
        const [ruasData, blocosData, baiasData, ocupados] = await Promise.all([
          getRuas(),
          getBlocos(),
          getBaias(),
          getLotesAlocadosEmEstoque(),
        ]);

        setRuas(ruasData ?? []);
        setBlocos(blocosData ?? []);
        setBaias(baiasData ?? []);

        // Agrupar os lotes por endereço (rua, bloco, baia)
        const lotesAgrupados = new Map();

        (ocupados ?? []).forEach((ocupado) => {
          const chave = `${ocupado.rua_id}-${ocupado.bloco_id}-${ocupado.baia_id}`;

          if (!lotesAgrupados.has(chave)) {
            lotesAgrupados.set(chave, {
              rua_id: ocupado.rua_id,
              bloco_id: ocupado.bloco_id,
              baia_id: ocupado.baia_id,
              lotes: [],
            });
          }

          // Adiciona o lote ao grupo
          lotesAgrupados.get(chave).lotes.push({
            id: ocupado.lote_id,
            numero_lote: ocupado.numero_lote,
          });
        });

        // Criar os endereços com os lotes agrupados
        const allEnderecos: EnderecoEstoque[] = [];
        (ruasData ?? []).forEach((rua: any) => {
          (blocosData ?? []).forEach((bloco: any) => {
            (baiasData ?? []).forEach((baia: any) => {
              // Verifica se o endereço está ocupado
              const chave = `${rua.id}-${bloco.id}-${baia.id}`;
              const enderecoOcupado = lotesAgrupados.get(chave);

              allEnderecos.push(
                enderecoOcupado
                  ? {
                      rua: { id: rua.id, nome: rua.nome },
                      bloco: { id: bloco.id, nome: bloco.nome },
                      baia: { id: baia.id, nome: baia.nome },
                      status: "ocupado",
                      lotes: enderecoOcupado.lotes,
                    }
                  : {
                      rua: { id: rua.id, nome: rua.nome },
                      bloco: { id: bloco.id, nome: bloco.nome },
                      baia: { id: baia.id, nome: baia.nome },
                      status: "livre",
                    }
              );
            });
          });
        });

        setEnderecos(allEnderecos);
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, []);

  // Efeito para buscar o número do lote ao digitar o NCC
  useEffect(() => {
    // Se o campo estiver vazio, limpa o filtro de lote
    if (!filtroNCCDebounced) {
      setFiltroNumeroLote("");
      return;
    }
    let ativo = true;
    getNumeroLotePorNCC(filtroNCCDebounced).then((numeroLote) => {
      if (!ativo) return;
      if (numeroLote) {
        setFiltroNumeroLote(numeroLote);
      } else {
        setFiltroNumeroLote("");
      }
    });
    return () => {
      ativo = false;
    };
  }, [filtroNCCDebounced]);

  // Filtragem reativa dos endereços
  const enderecosFiltrados = enderecos.filter((endereco) => {
    // Filtro status
    if (filtroStatus !== "todos" && endereco.status !== filtroStatus) {
      return false;
    }
    // Filtro número do lote (contém)
    if (
      filtroNumeroLote &&
      (!endereco.lotes ||
        endereco.lotes.length === 0 ||
        !endereco.lotes.some((lote) =>
          lote.numero_lote
            ?.toLowerCase()
            .includes(filtroNumeroLote.toLowerCase())
        ))
    ) {
      return false;
    }
    // Filtro rua
    if (filtroRua && endereco.rua.id !== filtroRua) {
      return false;
    }
    // Filtro bloco
    if (filtroBloco && endereco.bloco.id !== filtroBloco) {
      return false;
    }
    // Filtro baia
    if (filtroBaia && endereco.baia.id !== filtroBaia) {
      return false;
    }
    return true;
  });

  const handlerScroll = (el: any) => {
    if (!el) return;
    let isDragging = false;
    let startX = 0;
    let startY = 0;
    let scrollLeft = 0;
    let scrollTop = 0;

    // Funções para mouse
    const onMouseDown = (e: MouseEvent) => {
      isDragging = true;
      startX = e.pageX;
      startY = e.pageY;
      scrollLeft = el.scrollLeft;
      scrollTop = el.scrollTop;
      el.style.cursor = "grabbing";
    };
    const onMouseMove = (e: MouseEvent) => {
      if (!isDragging) return;
      e.preventDefault();
      const dx = e.pageX - startX;
      const dy = e.pageY - startY;
      el.scrollLeft = scrollLeft - dx;
      el.scrollTop = scrollTop - dy;
    };
    const onMouseUp = () => {
      isDragging = false;
      el.style.cursor = "grab";
    };

    // Funções para touch
    const onTouchStart = (e: TouchEvent) => {
      if (e.touches.length !== 1) return;
      isDragging = true;
      startX = e.touches[0].pageX;
      startY = e.touches[0].pageY;
      scrollLeft = el.scrollLeft;
      scrollTop = el.scrollTop;
    };
    const onTouchMove = (e: TouchEvent) => {
      if (!isDragging || e.touches.length !== 1) return;
      const dx = e.touches[0].pageX - startX;
      const dy = e.touches[0].pageY - startY;
      el.scrollLeft = scrollLeft - dx;
      el.scrollTop = scrollTop - dy;
    };
    const onTouchEnd = () => {
      isDragging = false;
    };

    // Adiciona listeners
    el.addEventListener("mousedown", onMouseDown);
    document.addEventListener("mousemove", onMouseMove);
    document.addEventListener("mouseup", onMouseUp);

    el.addEventListener("touchstart", onTouchStart, { passive: false });
    el.addEventListener("touchmove", onTouchMove, { passive: false });
    el.addEventListener("touchend", onTouchEnd);

    // Remove listeners ao desmontar
    return () => {
      el.removeEventListener("mousedown", onMouseDown);
      document.removeEventListener("mousemove", onMouseMove);
      document.removeEventListener("mouseup", onMouseUp);

      el.removeEventListener("touchstart", onTouchStart);
      el.removeEventListener("touchmove", onTouchMove);
      el.removeEventListener("touchend", onTouchEnd);
    };
  };

  return (
    <div className="planta-estoque-page">
      <h1 className="text-2xl font-bold mb-4">Planta Baixa do Estoque</h1>
      {/* Formulário de filtros com Collapsible */}
      <Collapsible
        open={filtersOpen}
        onOpenChange={setFiltersOpen}
        className="w-full mb-4"
      >
        <div className="flex justify-end items-center mb-4">
          {/* <h2 className="text-lg font-semibold">Filtros</h2> */}
          <CollapsibleTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className={
                hasActiveFilters()
                  ? "border-destructive text-destructive hover:bg-destructive/10"
                  : ""
              }
            >
              <FilterIcon className="h-4 w-4 mr-2" />
              {filtersOpen ? "Ocultar Filtros" : "Mostrar Filtros"}
              {hasActiveFilters() && (
                <span className="ml-2 rounded-full bg-destructive text-destructive-foreground px-2 py-0.5 text-xs">
                  {countActiveFilters()}
                </span>
              )}
            </Button>
          </CollapsibleTrigger>
        </div>

        <CollapsibleContent>
          <form
            className="flex flex-wrap gap-4"
            onSubmit={(e) => e.preventDefault()}
            autoComplete="off"
          >
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="filtro-ncc"
              >
                NCC
              </label>
              <input
                id="filtro-ncc"
                type="text"
                className="border rounded px-2 py-1 w-40"
                value={filtroNCC}
                onChange={(e) => setFiltroNCC(e.target.value)}
                placeholder="Digite o NCC"
                autoComplete="off"
              />
            </div>
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="filtro-lote"
              >
                Nº do Lote
              </label>
              <input
                id="filtro-lote"
                type="text"
                className="border rounded px-2 py-1 w-40"
                value={filtroNumeroLote}
                onChange={(e) => setFiltroNumeroLote(e.target.value)}
                placeholder="Digite o número do lote"
              />
            </div>
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="filtro-rua"
              >
                Rua
              </label>
              <select
                id="filtro-rua"
                className="border rounded px-2 py-1 w-32"
                value={filtroRua ?? ""}
                onChange={(e) =>
                  setFiltroRua(e.target.value ? e.target.value : undefined)
                }
              >
                <option value="">Todas</option>
                {ruas.map((rua) => (
                  <option key={rua.id} value={rua.id}>
                    {rua.nome}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="filtro-bloco"
              >
                Bloco
              </label>
              <select
                id="filtro-bloco"
                className="border rounded px-2 py-1 w-32"
                value={filtroBloco ?? ""}
                onChange={(e) =>
                  setFiltroBloco(e.target.value ? e.target.value : undefined)
                }
              >
                <option value="">Todos</option>
                {blocos.map((bloco) => (
                  <option key={bloco.id} value={bloco.id}>
                    {bloco.nome}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="filtro-baia"
              >
                Baia
              </label>
              <select
                id="filtro-baia"
                className="border rounded px-2 py-1 w-32"
                value={filtroBaia ?? ""}
                onChange={(e) =>
                  setFiltroBaia(e.target.value ? e.target.value : undefined)
                }
              >
                <option value="">Todas</option>
                {baias.map((baia) => (
                  <option key={baia.id} value={baia.id}>
                    {baia.nome}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="filtro-status"
              >
                Status
              </label>
              <select
                id="filtro-status"
                className="border rounded px-2 py-1 w-40"
                value={filtroStatus}
                onChange={(e) =>
                  setFiltroStatus(
                    e.target.value as "todos" | "livre" | "ocupado"
                  )
                }
              >
                <option value="todos">Todos</option>
                <option value="livre">Apenas vagas livres</option>
                <option value="ocupado">Apenas ocupadas</option>
              </select>
            </div>
            <div className="flex items-end">
              <Button
                variant={"outline"}
                onClick={() => {
                  setFiltroNumeroLote("");
                  setFiltroRua(undefined);
                  setFiltroBloco(undefined);
                  setFiltroBaia(undefined);
                  setFiltroNCC("");
                  setFiltroStatus("todos");
                }}
              >
                Limpar filtros
              </Button>
            </div>
          </form>
        </CollapsibleContent>
      </Collapsible>

      {/* Legenda visual */}
      <div className="flex justify-end items-center gap-4 mb-2">
        <span className="flex items-center gap-1">
          <span role="img" aria-label="Ocupado" className="text-lg">
            <div className="bg-red-600 text-white border-red-800 w-4 h-4"></div>
          </span>
          <span className="text-sm text-gray-700">Ocupado</span>
        </span>
        <span className="flex items-center gap-1">
          <span role="img" aria-label="Livre" className="text-lg">
            <div className="bg-green-600 text-white border-green-800 w-4 h-4"></div>
          </span>
          <span className="text-sm text-gray-700">Livre</span>
        </span>
      </div>
      {loading ? (
        <div>Carregando...</div>
      ) : enderecosFiltrados.length === 0 ? (
        <div className="text-center text-gray-500 py-8">
          Nenhum lote encontrado para os filtros selecionados.
        </div>
      ) : (
        <div
          ref={(el) => handlerScroll(el)}
          className={`w-screen h-screen ${
            filtersOpen
              ? "max-h-[calc(100vh-310px)]"
              : "max-h-[calc(100vh-240px)]"
          } overflow-scroll max-w-full ${
            state === "collapsed"
              ? "md:max-w-[calc(100vw-var(--sidebar-width-icon)-4rem)]"
              : "md:max-w-[calc(100vw-var(--sidebar-width)-2rem)]"
          }`}
          style={{ cursor: "grab" }}
        >
          <PlantaEstoque enderecos={enderecosFiltrados} />
        </div>
      )}
    </div>
  );
}
