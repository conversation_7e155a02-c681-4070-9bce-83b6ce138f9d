"use client";

import React, { useEffect } from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { DataTablePagination } from "./data-table-pagination";
import { useRouter } from "next/navigation";
import { DataTableToolbarLote } from "./data-table-tollbar-lote";
import { createClient } from "../../../../../../utils/supabase/client";
import { LoteAlocado } from "@/data/model/Desalocacao";
import {
  getDesalocacaoId,
  getLotesParaDesalocacaoExterrna,
  getLotesParaDesalocacaoInterna,
  insertDesalocacao,
} from "@/service/desalocacoes-service";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { newMovimentacaoInterna } from "@/service/movimentacoes-internas-service";
import { Lote } from "@/data/model/Lote";

interface DesalocacaoFormNewProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  initialFilterLoteId?: string | null;
  initialFilterNumeroLote?: string | null;
}

export function DesalocacaoFormNew<TData, TValue>({
  columns,
  data: initialData,
  initialFilterLoteId,
  initialFilterNumeroLote,
}: DesalocacaoFormNewProps<TData, TValue>) {
  const supabase = createClient();
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [saveDisabled, setSaveDisabled] = React.useState(false);
  const [movimentacaoInterna, setMovimentacaoInterna] = React.useState(false);
  const [tableData, setTableData] = React.useState<TData[]>(initialData);
  const [selectedRow, setSelectedRow] = React.useState<TData | null>(null);
  const router = useRouter();

  const table = useReactTable({
    data: tableData,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  async function handleSave() {
    const listDesalocacaoItem: any[] = [];
    const idDesalocacao = await getDesalocacaoId().catch((error) => {
      toast({
        duration: 2000,
        variant: "destructive",
        title: "Erro: Desalocação de Estoque",
        description: (
          <>
            <p>Erro ao gravar.</p>
            <p>{error.message}</p>
          </>
        ),
      });
    });

    if (table.getSelectedRowModel().rows.length <= 0) {
      toast({
        variant: "destructive",
        duration: 2000,
        title: "Alerta: Desalocação de Estoque",
        description: <p>Você precisa selecionar pelo menos um lote!</p>,
      });
      return;
    }

    setSaveDisabled(true);

    // obtem os lotes a serem adicionados na desalocacao de estoque
    // cria lista de itens_desalocacoes
    table.getSelectedRowModel().rows.map((row) => {
      const loteAlocado = row.original as Lote;

      listDesalocacaoItem.push({
        lote_id: loteAlocado.id,
        desalocacao_id: idDesalocacao,
      });
    });

    if (movimentacaoInterna) {
      const createdMovimentacaoInterna = await newMovimentacaoInterna(
        idDesalocacao!
      );

      if (createdMovimentacaoInterna) {
        //Algo aqui
      }
    }

    await insertDesalocacao(listDesalocacaoItem)
      .then(() => {
        toast({
          duration: 2000,
          title: "Desalocação de Estoque",
          description: <p>Desalocação de Estoque gravada com sucesso!</p>,
        });
        router.push("/estoque/desalocacoes");
      })
      .catch((error) => {
        toast({
          duration: 2000,
          variant: "destructive",
          title: "Erro: Desalocação de Estoque",
          description: (
            <>
              <p>Erro ao gravar.</p>
              <p>{error.message}</p>
            </>
          ),
        });
      })
      .finally(() => {
        setSaveDisabled(false);
      });
  }

  useEffect(() => {
    if (movimentacaoInterna) {
      const getLotesDisponivel = async () => {
        const lotesMovimentacaoInterna =
          (await getLotesParaDesalocacaoInterna()) as unknown as TData[];

        if (lotesMovimentacaoInterna) {
          setTableData(lotesMovimentacaoInterna);
        }
      };

      getLotesDisponivel().catch(() => {
        console.error("Erro ao obter dados do Supabase.");
      });
    } else {
      const getLotesDisponivel = async () => {
        const lotesMovimentacaoExterna =
          (await getLotesParaDesalocacaoExterrna()) as unknown as TData[];

        if (lotesMovimentacaoExterna) {
          setTableData(lotesMovimentacaoExterna);
        }
      };

      getLotesDisponivel().catch(() => {
        console.error("Erro ao obter dados do Supabase.");
      });
    }
  }, [movimentacaoInterna]);

  function onMovimentacaoInternaChanged() {
    setMovimentacaoInterna(!movimentacaoInterna);
  }

  useEffect(() => {
    if (initialFilterNumeroLote && table.getColumn("numero_lote")) {
      table.getColumn("numero_lote")?.setFilterValue(initialFilterNumeroLote);

      // Encontrar e selecionar automaticamente o lote correspondente
      if (tableData.length > 0) {
        const loteIndex = tableData.findIndex(
          (lote: any) =>
            (initialFilterLoteId && lote.id === initialFilterLoteId) ||
            (initialFilterNumeroLote &&
              lote.numero_lote === initialFilterNumeroLote)
        );

        if (loteIndex >= 0) {
          table.setRowSelection({ [loteIndex]: true });

          // Se encontrou o lote, já seleciona ele automaticamente
          const selectedLote = tableData[loteIndex];
          if (selectedLote) {
            setSelectedRow(selectedLote);
          }
        }
      }
    }
  }, [tableData, initialFilterLoteId, initialFilterNumeroLote, table]);

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <Switch
          checked={movimentacaoInterna}
          onCheckedChange={onMovimentacaoInternaChanged}
        />
        <span>Movimentação Interna</span>
      </div>
      <DataTableToolbarLote table={table} />
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} colSpan={header.colSpan}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  Nenhum resultado.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination table={table} />

      <Button onClick={handleSave} variant="outline" disabled={saveDisabled}>
        Salvar
      </Button>
    </div>
  );
}
