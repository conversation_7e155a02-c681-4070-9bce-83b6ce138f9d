"use client";

import React, { useEffect, useState } from "react";
import { DesalocacaoFormNew } from "./desaloacao-form-new";
import { columnsLote } from "./columns-lote";
import { Skeleton } from "@/components/ui/skeleton";
import { getLotesAlocados } from "@/service/alocacoes-service";
import { toast } from "@/hooks/use-toast";
import { useSearchParams } from "next/navigation";

export default function DesalocacaoNew() {
  const [registros, setRegistros] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<Boolean>(true);
  const searchParams = useSearchParams();
  const loteIdParam = searchParams.get("lote_id");
  const numeroLoteParam = searchParams.get("numero_lote");

  useEffect(() => {
    const getLotesDisponivel = async () => {
      await getLotesAlocados()
        .then((lotesAlocados) => {
          setRegistros(lotesAlocados ? lotesAlocados : []);
        })
        .catch((error) => {
          toast({
            duration: 2000,
            variant: "destructive",
            title: "Erro: Desalocação de Estoque",
            description: (
              <>
                <p>Erro ao carregar Lotes Alocados.</p>
                <p>{error.message}</p>
              </>
            ),
          });
        });
    };

    getLotesDisponivel().finally(() => {
      setIsLoading(false);
    });
  }, []);

  return (
    <>
      {isLoading ? (
        <Skeleton className="h-60 rounded-sm" />
      ) : (
        <DesalocacaoFormNew
          data={registros}
          columns={columnsLote}
          initialFilterLoteId={loteIdParam}
          initialFilterNumeroLote={numeroLoteParam}
        />
      )}
    </>
  );
}
