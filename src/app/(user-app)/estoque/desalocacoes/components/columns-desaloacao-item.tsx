'use client';

import { But<PERSON> } from '@/components/ui/button';
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuItem,
//   DropdownMenuLabel,
//   DropdownMenuTrigger,
// } from '@/components/ui/dropdown-menu';
import { DesalocacaoItem } from '@/data/model/Desalocacao';
import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown, MoreHorizontal } from 'lucide-react';
// import { useRouter } from 'next/navigation';

// function ActionMenu({ id }: { id: string }) {
//   const router = useRouter();

//   const handleViewDetails = () => {
//     router.push(`desalocacoes/${id}`);
//   };

//   return (
//     <DropdownMenu>
//       <DropdownMenuTrigger asChild>
//         <Button variant='ghost' className='h-8 w-8 p-0'>
//           <span className='sr-only'>Abrir Menu</span>
//           <MoreHorizontal className='h-4 w-4' />
//         </Button>
//       </DropdownMenuTrigger>
//       <DropdownMenuContent align='end'>
//         <DropdownMenuLabel>Ações</DropdownMenuLabel>
//         <DropdownMenuItem onClick={handleViewDetails}>
//           Visualizar Registro
//         </DropdownMenuItem>
//       </DropdownMenuContent>
//     </DropdownMenu>
//   );
// }

export const columnsDesalocacaoItem: ColumnDef<DesalocacaoItem>[] = [
  // {
  //   id: 'actions',
  //   cell: ({ row }) => {
  //     const portion = row.original;

  //     return <ActionMenu id={portion.id!} />;
  //   },
  // },
  // {
  //   accessorKey: 'id',
  //   header: ({ column }) => {
  //     return (
  //       <Button
  //         variant='ghost'
  //         onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
  //       >
  //         ID
  //         <ArrowUpDown className='ml-2 h-4 w-4' />
  //       </Button>
  //     );
  //   },
  // },
  {
    accessorKey: 'numero_lote',
    header: ({ column }) => {
      return (
        <Button
          variant='ghost'
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Número do Lote
          <ArrowUpDown className='ml-2 h-4 w-4' />
        </Button>
      );
    },
  },
];
