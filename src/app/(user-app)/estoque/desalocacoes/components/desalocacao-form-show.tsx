'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { CalendarIcon } from '@radix-ui/react-icons';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { DataTable } from './data-table';
import { columnsDesalocacaoItem } from './columns-desaloacao-item';
import { DesalocacaoItem } from '@/data/model/Desalocacao';

const desalocacaoFormSchema = z.object({
  id: z.string().optional(),
  created_at: z.date().optional(),
  email: z.string().optional(),
  numero_controle: z.string().optional(),
});

type DesalocacaoFormValues = z.infer<typeof desalocacaoFormSchema>;

export function DesalocacaoFormShow({
  initialData,
  itemsDesalocacao,
}: {
  initialData: any;
  itemsDesalocacao: DesalocacaoItem[] | null;
}) {
  const form = useForm<DesalocacaoFormValues>({
    resolver: zodResolver(desalocacaoFormSchema),
    defaultValues: initialData,
    mode: 'onChange',
  });

  async function onSubmit(data: any) {}

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>
          <FormField
            control={form.control}
            name='numero_controle'
            render={({ field }) => (
              <FormItem>
                <FormLabel>NÚMERO DE CONTROLE</FormLabel>
                <FormControl>
                  <Input placeholder='' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='email'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Usuário</FormLabel>
                <FormControl>
                  <Input placeholder='' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='created_at'
            render={({ field }) => (
              <FormItem className='flex flex-col'>
                <FormLabel>Data de Criação</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'w-[240px] pl-3 text-left font-normal',
                          !field.value && 'text-muted-foreground'
                        )}
                      >
                        {field.value ? (
                          format(field.value, 'dd/MM/yyyy HH:mm')
                        ) : (
                          <span>Selecione uma Data</span>
                        )}
                        <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className='w-auto p-0' align='start'>
                    <Calendar
                      mode='single'
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) =>
                        date > new Date() || date < new Date('1900-01-01')
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </form>
      </Form>
      <div className='mt-6'>
        <span className='font-medium text-sm leading-none'>
          Itens da Desalocação
        </span>
        <DataTable
          showToolbar={false}
          columns={columnsDesalocacaoItem}
          data={itemsDesalocacao ? itemsDesalocacao : []}
        />
      </div>
    </>
  );
}
