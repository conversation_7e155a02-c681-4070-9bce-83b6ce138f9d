"use client";

import { useEffect, useState, useCallback } from "react";
import { columns } from "./columns";
import { Skeleton } from "@/components/ui/skeleton";
import { DataTable } from "@/components/data-table/data-table-ref"; // Importa o DataTable correto
import { createClient } from "../../../../../../utils/supabase/client";
import { DesalocacaoList } from "@/data/model/Desalocacao";
import { useSearchParams, useRouter } from "next/navigation"; // Importa hooks do Next.js

export default function DesalocacaoPageDetail() {
  const supabase = createClient();
  const searchParams = useSearchParams(); // Hook para parâmetros de URL
  const router = useRouter(); // Hook para navegação

  // Estados para paginação e filtro
  const page = parseInt(searchParams.get("page") || "1");
  const pageSize = parseInt(searchParams.get("pageSize") || "10");
  const search = searchParams.get("search") || "";

  const [registros, setRegistros] = useState<DesalocacaoList[]>([]);
  const [isLoading, setIsLoading] = useState<Boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0); // Estado para total de registros

  // useEffect para buscar dados quando a página, tamanho da página ou busca mudam
  useEffect(() => {
    getDesalocacao(search);
  }, [page, pageSize, search]);

  // Função para buscar desalocações com paginação e filtro
  const getDesalocacao = useCallback(
    async (filterValue: string = "") => {
      setIsLoading(true);
      setError(null);
      const from = (page - 1) * pageSize; // Índice inicial para Supabase range
      const to = from + pageSize - 1; // Índice final para Supabase range

      let query = supabase.from("vw_desalocacoes_list").select(
        `
          id,
          created_at,
          numero_controle,
          user_email
        `,
        { count: "exact" } // Solicita a contagem total
      );

      // Aplica filtro se houver valor
      if (filterValue) {
        // Implementar lógica de filtro no Supabase para outros campos se necessário
        query = query.or(
          `numero_controle.ilike.%${filterValue}%,created_at_str.ilike.%${filterValue}%,user_email.ilike.%${filterValue}%`
        );
      }

      try {
        const {
          data: desalocacoesData,
          error: desalocacoesError,
          count, // Recebe a contagem total
        } = await query.range(from, to); // Aplica paginação

        if (desalocacoesError) throw desalocacoesError;

        if (!desalocacoesData) {
          setRegistros([]);
          setTotalCount(0); // Define totalCount como 0
          return;
        }

        setRegistros(desalocacoesData);
        setTotalCount(count || 0); // Define o total de registros
      } catch (err: any) {
        console.error("Erro ao buscar dados:", err);
        setError(err.message);
        setRegistros([]);
        setTotalCount(0); // Define totalCount como 0 em caso de erro
      } finally {
        setIsLoading(false);
      }
    },
    [page, pageSize, search] // Dependências do useCallback
  );

  // Função para lidar com a mudança de página ou tamanho da página
  const onPaginationChange = (newPage: number, newPageSize: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("page", newPage.toString());
    params.set("pageSize", newPageSize.toString());
    router.push(`?${params.toString()}`);
  };

  // Função para lidar com a mudança no valor de busca
  const onSearchChange = (value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    if (value && value !== "") {
      if (value !== search) {
        params.set("page", "1");
      }
    }
    params.set("search", value);
    router.push(`?${params.toString()}`);
  };

  if (error) {
    return (
      <div className="text-red-500 p-4">Erro ao carregar dados: {error}</div>
    );
  }

  return (
    <>
      {isLoading ? (
        <Skeleton className="h-2/3 rounded-sm" />
      ) : (
        <DataTable
          showToolbar={true}
          data={registros}
          columns={columns}
          page={page} // Passa a página atual
          pageSize={pageSize} // Passa o tamanho da página
          totalCount={totalCount} // Passa o total de registros
          onPaginationChange={onPaginationChange} // Passa a função de mudança de paginação
          onSearchChange={onSearchChange} // Passa a função de mudança de busca
          search={search} // Passa o valor de busca
        />
      )}
    </>
  );
}
