import { Metada<PERSON> } from 'next';
import { Button } from '@/components/ui/button';
import ButtonLink from '@/components/app-button-link';
import DesalocacaoPageDetail from './components/page-detail';

export const metadata: Metadata = {
  title: 'Desalocação de Estoque',
  description: 'Listagem de Desalocações de Estoque',
};

export default async function DesalocacaoPage() {
  return (
    <>
      <div className='hidden h-full flex-1 flex-col space-y-8 p-8 md:flex'>
        <div className='flex items-center justify-between space-y-2'>
          <div className='flex justify-between w-full'>
            <h2 className='text-2xl font-bold tracking-tight'>
              Desalocações de Estoque
            </h2>
            <ButtonLink url='desalocacoes/new'>
              {' '}
              <Button variant='outline'>Novo</Button>
            </ButtonLink>
          </div>
        </div>
        <DesalocacaoPageDetail />
      </div>
    </>
  );
}
