import {
  getDesalocacaoById,
  getDesalocacaoItens,
} from '@/service/desalocacoes-service';
import { createClient } from '../../../../../../utils/supabase/server';
import { DesalocacaoFormShow } from '../components/desalocacao-form-show';
import DesalocacaoNew from '../components/desalocacao-new';
import { toast } from '@/hooks/use-toast';

export default async function DesalocacaoEditPage({ params }: { params: any }) {
  const { id } = params;
  const supabase = createClient();
  let desalocacaoEstoque;
  let itensDesalocacao;

  if (id !== 'new') {
    await getDesalocacaoById(id)
      .then((res) => {
        desalocacaoEstoque = res;
      })
      .catch((error) => {
        toast({
          duration: 2000,
          variant: 'destructive',
          title: 'Erro: Desalocação de Estoque',
          description: (
            <>
              <p>Erro ao carregar Desalocação de Estoque.</p>
              <p>{error.message}</p>
            </>
          ),
        });
      });
    await getDesalocacaoItens(id)
      .then((res) => {
        itensDesalocacao = res;
      })
      .catch((error) => {
        toast({
          duration: 2000,
          variant: 'destructive',
          title: 'Erro: Desalocação de Estoque',
          description: (
            <>
              <p>Erro ao carregar Itens da Desalocação de Estoque.</p>
              <p>{error.message}</p>
            </>
          ),
        });
      });
  }

  return (
    <div className='container mx-auto py-4'>
      <h1 className='text-2xl font-bold mb-4'>
        {id === 'new'
          ? 'Nova Desalocação de Estoque'
          : `Consultar Desalocação de Estoque: ${
              desalocacaoEstoque!.numero_controle
            }`}
      </h1>
      {id === 'new' ? (
        <DesalocacaoNew />
      ) : (
        <DesalocacaoFormShow
          itemsDesalocacao={itensDesalocacao ? itensDesalocacao : []}
          initialData={desalocacaoEstoque}
        />
      )}
    </div>
  );
}
