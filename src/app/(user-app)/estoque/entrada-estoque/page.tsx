import { Metada<PERSON> } from 'next';
import { But<PERSON> } from '@/components/ui/button';
import ButtonLink from '@/components/app-button-link';
import EntradaEstoquePageDetail from './components/page-detail';

export const metadata: Metadata = {
  title: 'Entrada de Estoque',
  description: 'Listagem de Entradas de Estoque',
};

export default async function EntradaEstoquePage() {
  return (
    <>
      <div className='hidden h-full flex-1 flex-col space-y-8 p-8 md:flex'>
        <div className='flex items-center justify-between space-y-2'>
          <div className='flex justify-between w-full'>
            <h2 className='text-2xl font-bold tracking-tight'>
              Entradas de Estoque
            </h2>
            <ButtonLink url='entrada-estoque/new'>
              {' '}
              <Button variant='outline'>Novo</Button>
            </ButtonLink>
          </div>
        </div>
        <EntradaEstoquePageDetail />
      </div>
    </>
  );
}
