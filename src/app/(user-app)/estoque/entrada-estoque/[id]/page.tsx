import { createClient } from "../../../../../../utils/supabase/server";
import { columnsLote } from "../components/columns-lote";
import { EntradaEstoqueFormNew } from "../components/entrada-estoque-form-new";
import { EntradaEstoqueFormShow } from "../components/entrada-estoque-form-show";

async function getEntradaEstoque(entrada_id: string): Promise<any | null> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from("entradas_estoque")
      .select(`id, created_at, numero_controle, user_profiles (email)`)
      .eq("id", entrada_id);

    if (!error && data) {
      // retira o objeto aninhado
      const formattedData = data.map((item: any) => ({
        ...item,
        email: item.user_profiles.email,
      }));

      // Remover o campo `user_profiles` agora desnecessário
      formattedData.forEach((item) => delete item.user_profiles);

      return formattedData[0];
    }
  } catch (error) {
    console.error("Erro ao buscar dados para do Supabase:", error);
  }

  return null;
}

async function getEntradaEstoqueItens(entrada_id: string): Promise<any[]> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from("itens_entrada_estoque")
      .select(`id, lotes (numero_lote)`)
      .eq("entrada_estoque_id", entrada_id)
      .eq("ativo", true);

    if (!error && data) {
      // retira o objeto aninhado
      const formattedData = data.map((item: any) => ({
        ...item,
        numero_lote: item.lotes.numero_lote,
      }));

      // Remover o campo `lotes` agora desnecessário
      formattedData.forEach((item) => delete item.lotes);

      return formattedData;
    }
  } catch (error) {
    console.error("Erro ao buscar dados para do Supabase:", error);
  }

  return [];
}

export default async function EntradaEstoqueEditPage({
  params,
}: {
  params: any;
}) {
  const { id } = params;
  const entradaEstoque = await getEntradaEstoque(id);
  const itensEntradaEstoque = await getEntradaEstoqueItens(id);
  // const lotesDisponivel = await getLotesDisponivel();

  return (
    <div className="container mx-auto py-4">
      <h1 className="text-2xl font-bold mb-4">
        {id === "new"
          ? "Nova Entrada de Estoque"
          : `Consultar Entrada de Estoque: ${entradaEstoque?.numero_controle}`}
      </h1>
      {id === "new" ? (
        <EntradaEstoqueFormNew columns={columnsLote} />
      ) : (
        <EntradaEstoqueFormShow
          itemsEntradaEstoque={itensEntradaEstoque}
          initialData={entradaEstoque}
        />
      )}
    </div>
  );
}
