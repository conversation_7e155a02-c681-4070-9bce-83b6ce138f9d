"use client";

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Lote } from "@/data/model/Lote";
import { toast } from "@/hooks/use-toast";
import { DataTablePaginationLote } from "./data-table-pagination-lote";
import { useRouter } from "next/navigation";
import { DataTableToolbarLote } from "./data-table-tollbar-lote";
import { createClient } from "../../../../../../utils/supabase/client";
interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
}

type ItemLotesEntradaEstoque = {
  id: string;
  created_at: Date;
  numero_lote: string;
};

export function EntradaEstoqueFormNew<TData, TValue>({
  columns,
}: DataTableProps<TData, TValue>) {
  const supabase = createClient();
  const [data, setData] = React.useState<TData[]>([]);
  const [rowCount, setRowCount] = React.useState(0);
  const [rowSelection, setRowSelection] = React.useState({});
  const [selectedItems, setSelectedItems] = React.useState<
    ItemLotesEntradaEstoque[]
  >([]);
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [pagination, setPagination] = React.useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [loading, setLoading] = React.useState(false);
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [saveDisabled, setSaveDisabled] = React.useState(false);
  const [loteFilter, setLoteFilter] = React.useState<string>("");
  const router = useRouter();
  const table = useReactTable({
    columns,
    data,
    rowCount,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      pagination,
    },
    enableRowSelection: true,
    getRowId: (row: any) => row.id,
    onRowSelectionChange: (updater) => {
      const newSelection =
        typeof updater === "function" ? updater(rowSelection) : updater;
      setRowSelection(newSelection);

      const selectedRowIds = Object.keys(newSelection);

      // Manter os que já estavam selecionados e não foram desmarcados
      const preserved = selectedItems.filter((item) =>
        selectedRowIds.includes((item as ItemLotesEntradaEstoque).id)
      );

      // Adicionar os novos da página atual
      const added = data.filter(
        (row) =>
          selectedRowIds.includes((row as ItemLotesEntradaEstoque).id) &&
          !preserved.find(
            (item) =>
              (item as ItemLotesEntradaEstoque).id ===
              (row as ItemLotesEntradaEstoque).id
          )
      );

      const updated = [...new Set([...preserved, ...added])];
      setSelectedItems(updated as ItemLotesEntradaEstoque[]);
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: (updater) => {
      setColumnFilters(updater); // Atualiza o estado da tabela com todos os filtros
      table.setPageIndex(0); // Resetar para a primeira página ao filtrar

      // Extrai o valor do filtro de lote para o estado local
      const newFilters =
        typeof updater === "function" ? updater(columnFilters) : updater;
      const loteFilterValue = newFilters.find(
        (f) => f.id === "numero_lote"
      )?.value;
      setLoteFilter(typeof loteFilterValue === "string" ? loteFilterValue : "");
    },
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    manualPagination: true, // Habilita paginação manual
    manualFiltering: true, // Habilita filtro manual
    manualSorting: true, // Habilita ordenação manual
    onPaginationChange: setPagination,
  });

  const fetchLotes = React.useCallback(async () => {
    setLoading(true);
    const { pageIndex, pageSize } = pagination;
    const from = pageIndex * pageSize;
    const to = from + pageSize - 1;

    const filtersObject: { [key: string]: string } = columnFilters.reduce(
      (acc: { [key: string]: string }, filter) => {
        if (typeof filter.value === "string") {
          acc[filter.id] = filter.value;
        }
        return acc;
      },
      {} as { [key: string]: string }
    );

    // Sobrescrever o filtro de lote com o valor direto
    if (loteFilter) {
      filtersObject["numero_lote"] = loteFilter;
    } else {
      // Se o loteFilter estiver vazio, garantir que o filtro de lote seja removido
      delete filtersObject["numero_lote"];
    }

    const sorts: { id: string; desc: boolean }[] = [];
    sorting.forEach((sort) => {
      sorts.push({ id: sort.id, desc: sort.desc });
    });

    const rpcArgs: {
      _offset: number;
      _limit: number;
      _filters?: object;
      _sorts?: string; // Tornar opcional
    } = {
      _offset: from,
      _limit: pageSize,
    };

    if (Object.keys(filtersObject).length > 0) {
      rpcArgs._filters = filtersObject;
    }

    if (sorts.length > 0) {
      rpcArgs._sorts = sorts
        .map((sort) => `${sort.id} ${sort.desc ? "desc" : "asc"}`)
        .join(", ");
    }

    const { data, error, count } = await supabase.rpc(
      "get_lotes_entrada_estoque",
      rpcArgs
    );

    if (error) {
      console.error("Erro ao buscar lotes:", error);
      setData([]);
      setRowCount(0);
    } else {
      setData(data as TData[]);
      setRowCount(data[0]?.total_count || 0); // Usar total_count da RPC
    }
    setLoading(false);
  }, [pagination, columnFilters, sorting, loteFilter]);

  React.useEffect(() => {
    fetchLotes();
  }, [pagination.pageIndex, pagination.pageSize, loteFilter, sorting]);

  async function getEntradaEstoqueId(): Promise<string | null> {
    try {
      const { data, error } = await supabase
        .from("entradas_estoque")
        .insert({})
        .select();

      if (!error && data) {
        return data[0].id;
      }
    } catch (error) {
      console.error("Erro ao inserir Entrada de Estoque no Supabase:", error);
    }

    return null;
  }

  async function insertEntradaEstoque(itemsEntradaEstoque: any[]) {
    // Insere itens da nova entrada de estoque
    try {
      const { data, error } = await supabase
        .from("itens_entrada_estoque")
        .insert(itemsEntradaEstoque)
        .select();

      if (!error && data) {
        setSaveDisabled(false);
        toast({
          duration: 3000,
          title: "Entrada de Estoque",
          description: <p>Entrada de Estoque gravada com sucesso!</p>,
        });
        router.push("/estoque/entrada-estoque");
      }
    } catch (error) {
      setSaveDisabled(false);
      console.error(
        "Erro ao inserir Itens de Entrada de Estoque para no Supabase:",
        error
      );
    }
  }

  async function handleSave() {
    const listEntradaEstoqueItem: any[] = [];
    const idEntradaEstoque = await getEntradaEstoqueId();

    if (selectedItems.length <= 0) {
      toast({
        variant: "destructive",
        duration: 2000,
        title: "Alerta: Entrada de Estoque",
        description: <p>Você precisa selecionar pelo menos um lote!</p>,
      });
      return;
    }

    setSaveDisabled(true);

    selectedItems.map((row) => {
      const itemEntradaEstoque = row as ItemLotesEntradaEstoque;
      listEntradaEstoqueItem.push({
        lote_id: itemEntradaEstoque.id,
        entrada_estoque_id: idEntradaEstoque,
      });
    });

    await insertEntradaEstoque(listEntradaEstoqueItem);

    setSaveDisabled(false);
  }

  return (
    <div className="space-y-4">
      <DataTableToolbarLote table={table} />
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} colSpan={header.colSpan}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  Carregando...
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  Nenhum resultado.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <DataTablePaginationLote
        table={table}
        selectedItemsCount={selectedItems.length}
      />

      <Button onClick={handleSave} variant="outline" disabled={saveDisabled}>
        Salvar
      </Button>
    </div>
  );
}
