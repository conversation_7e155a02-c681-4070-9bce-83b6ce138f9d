"use client";

import { useEffect, useState, useCallback } from "react";
import { columns } from "./columns";
import { Skeleton } from "@/components/ui/skeleton";
import { DataTable } from "@/components/data-table/data-table-ref";
import { createClient } from "../../../../../../utils/supabase/client";
import { EntradaEstoqueList } from "@/data/model/EntradaEstoque";
import { useSearchParams, useRouter } from "next/navigation";

export default function EntradaEstoquePageDetail() {
  const supabase = createClient();
  const searchParams = useSearchParams();
  const router = useRouter();
  const page = parseInt(searchParams.get("page") || "1");
  const pageSize = parseInt(searchParams.get("pageSize") || "10");
  const search = searchParams.get("search") || "";

  const [registros, setRegistros] = useState<EntradaEstoqueList[]>([]);
  const [isLoading, setIsLoading] = useState<Boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  useEffect(() => {
    getEntradaEstoque(search);
  }, [page, pageSize, search]);

  const getEntradaEstoque = useCallback(
    async (filterValue: string = "") => {
      setIsLoading(true);
      setError(null);
      const from = (page - 1) * pageSize; // 0-based index for Supabase range
      const to = from + pageSize - 1;

      let query = supabase.from("vw_entradas_estoque_list").select(
        `
        id,
        created_at,
        numero_controle,
        user_email
      `,
        { count: "exact" }
      );

      if (filterValue) {
        // Para mais filtros, implementar a lógica de filtro no Supabase
        query = query.or(
          `numero_controle.ilike.%${filterValue}%,created_at_str.ilike.%${filterValue}%,user_email.ilike.%${filterValue}%`
        );
      }

      try {
        const {
          data: entradasData,
          error: entradasError,
          count,
        } = await query.range(from, to);

        if (entradasError) throw entradasError;

        if (!entradasData) {
          setRegistros([]);
          setTotalCount(0);
          return;
        }

        setRegistros(entradasData);
        setTotalCount(count || 0);
      } catch (err: any) {
        console.error("Erro ao buscar dados:", err);
        setError(err.message);
        setRegistros([]);
        setTotalCount(0);
      } finally {
        setIsLoading(false);
      }
    },
    [page, pageSize, search]
  );

  const onPaginationChange = (newPage: number, newPageSize: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("page", newPage.toString());
    params.set("pageSize", newPageSize.toString());
    router.push(`?${params.toString()}`);
  };

  const onSearchChange = (value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    if (value && value !== "") {
      if (value !== search) {
        params.set("page", "1");
      }
    }
    params.set("search", value);
    router.push(`?${params.toString()}`);
  };

  if (error) {
    return (
      <div className="text-red-500 p-4">Erro ao carregar dados: {error}</div>
    );
  }

  return (
    <>
      {isLoading ? (
        <Skeleton className="h-2/3 rounded-sm" />
      ) : (
        <DataTable
          showToolbar={true}
          data={registros}
          columns={columns}
          page={page}
          pageSize={pageSize}
          totalCount={totalCount}
          onPaginationChange={onPaginationChange}
          onSearchChange={onSearchChange}
          search={search}
        />
      )}
    </>
  );
}
