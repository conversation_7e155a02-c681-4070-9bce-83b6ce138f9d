'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { CalendarIcon } from '@radix-ui/react-icons';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import {
  EntradaEstoque,
  EntradaEstoqueItem,
} from '@/data/model/EntradaEstoque';
import { DataTable } from './data-table';
import { columnsEntradaEstoqueItem } from './columns-entrada-estoque-item';

const entradaEstoqueFormSchema = z.object({
  id: z.string().optional(),
  created_at: z.date().optional(),
  email: z.string().optional(),
  numero_controle: z.string().optional(),
});

type EntradaEstoqueFormValues = z.infer<typeof entradaEstoqueFormSchema>;

export function EntradaEstoqueFormShow({
  initialData,
  itemsEntradaEstoque,
}: {
  initialData: any;
  itemsEntradaEstoque: EntradaEstoqueItem[] | null;
}) {
  const form = useForm<EntradaEstoqueFormValues>({
    resolver: zodResolver(entradaEstoqueFormSchema),
    defaultValues: initialData,
    mode: 'onChange',
  });

  async function onSubmit(data: any) {}

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>
          {/* <FormField
            control={form.control}
            name='id'
            render={({ field }) => (
              <FormItem>
                <FormLabel>ID</FormLabel>
                <FormControl>
                  <Input placeholder='' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          /> */}

          <FormField
            control={form.control}
            name='numero_controle'
            render={({ field }) => (
              <FormItem>
                <FormLabel>NÚMERO DE CONTROLE</FormLabel>
                <FormControl>
                  <Input placeholder='' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='email'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Usuário</FormLabel>
                <FormControl>
                  <Input placeholder='' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='created_at'
            render={({ field }) => (
              <FormItem className='flex flex-col'>
                <FormLabel>Data de Criação</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'w-[240px] pl-3 text-left font-normal',
                          !field.value && 'text-muted-foreground'
                        )}
                      >
                        {field.value ? (
                          format(field.value, 'dd/MM/yyyy HH:mm')
                        ) : (
                          <span>Selecione uma Data</span>
                        )}
                        <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className='w-auto p-0' align='start'>
                    <Calendar
                      mode='single'
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) =>
                        date > new Date() || date < new Date('1900-01-01')
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </form>
      </Form>
      <div className='mt-6'>
        <span className='font-medium text-sm leading-none'>
          Itens da Entrada de Estoque
        </span>
        <DataTable
          showToolbar={false}
          columns={columnsEntradaEstoqueItem}
          data={itemsEntradaEstoque ? itemsEntradaEstoque : []}
        />
      </div>
    </>
  );
}
