import { Alocacao, AlocacaoItem } from '@/data/model/Alocacao';
import {
  getAlocacaoById,
  getItemsAlocacaoByAlocacaoId,
} from '@/service/alocacoes-service';
import React from 'react';
import AlocacaoDetalhesPage from '../components/alocacao-detalhes';
import { AlocacaoDetalhesPageNew } from '../components/alocacao-detalhes-new';

function formatDate(createdAt: string): string {
  const date = new Date(createdAt);
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  }).format(date);
}

async function getAlocacao(id: string) {
  let alocacaoFromDatabase: Alocacao | undefined;
  await getAlocacaoById(id)
    .then((res) => {
      alocacaoFromDatabase = res;
    })
    .catch((error) => {
      console.error(error);
    });

  if (alocacaoFromDatabase) {
    return alocacaoFromDatabase;
  }
}

async function getItensAlocacao(id: string) {
  let itensAlocacaoFromDatabase: AlocacaoItem[] | undefined;

  await getItemsAlocacaoByAlocacaoId(id)
    .then((res) => {
      itensAlocacaoFromDatabase = res;
    })
    .catch((error) => {
      console.error(error);
    });
  return itensAlocacaoFromDatabase ?? [];
}

async function AlocacaoActionPage({ params }: { params: any }) {
  const { id } = params;
  let alocacao;
  let itemsAlocacao;
  if (id !== 'new') {
    alocacao = await getAlocacao(id);
    itemsAlocacao = await getItensAlocacao(id);
  }

  return (
    <div className='container mx-auto py-4'>
      <h1 className='text-2xl font-bold mb-4'>
        {id === 'new'
          ? 'Nova Alocação de Estoque'
          : `Consultar Alocação de Estoque: ${
              alocacao ? alocacao?.numero_controle : ''
            }`}
      </h1>
      {id === 'new' ? (
        <AlocacaoDetalhesPageNew />
      ) : (
        <AlocacaoDetalhesPage
          itemsAlocacao={itemsAlocacao}
          initialData={alocacao}
        />
      )}
    </div>
  );
}

export default AlocacaoActionPage;
