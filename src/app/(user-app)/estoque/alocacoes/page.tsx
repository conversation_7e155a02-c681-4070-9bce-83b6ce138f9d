import ButtonLink from '@/components/app-button-link';
import { Button } from '@/components/ui/button';
import { Metadata } from 'next';
import React from 'react';
import AlocacoesPageList from './components/page-list';

export const metadata: Metadata = {
  title: 'Alocação de Estoque',
  description: 'Listagem de Alocações de Estoque',
};

function AlocacoesPage() {
  return (
    <>
      <div className='hidden h-full flex-1 flex-col space-y-8 p-8 md:flex'>
        <div className='flex items-center justify-between space-y-2'>
          <div className='flex justify-between w-full'>
            <h2 className='text-2xl font-bold tracking-tight'>
              Alocações de Estoque
            </h2>
            <ButtonLink url='/estoque/alocacoes/new'>
              {' '}
              <Button variant='outline'>Novo</Button>
            </ButtonLink>
          </div>
        </div>
        <AlocacoesPageList />
      </div>
    </>
  );
}

export default AlocacoesPage;
