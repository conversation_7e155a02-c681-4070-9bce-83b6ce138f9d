import { z } from 'zod';

export const AlocacaoTypeSchema = z.object({
  id: z.string(),
  //created_at: z.string(),
  created_at: z.date().optional(),
  numero_controle: z.string().optional(),
  user_email: z.string().optional(),
  rua_id: z.string().optional(),
  bloco_id: z.string().optional(),
  baia_id: z.string().optional(),
  // rua: z.string(),
  // bloco: z.string(),
  // baia: z.string(),
});

export const AlocacaoTypeSchemaNew = z.object({
  rua_id: z.string({
    required_error: 'Informe uma Rua.',
  }),
  bloco_id: z.string({
    required_error: 'Informe um Bloco.',
  }),
  baia_id: z.string({
    required_error: 'Informe uma Baia.',
  }),
});

export type ImportedFile = z.infer<typeof AlocacaoTypeSchema>;
