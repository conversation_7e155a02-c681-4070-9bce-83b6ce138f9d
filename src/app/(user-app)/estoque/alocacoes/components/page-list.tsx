"use client";

import { useEffect, useState, useCallback } from "react";
import { columns } from "./columns";
import { Skeleton } from "@/components/ui/skeleton";
import { DataTable } from "@/components/data-table/data-table-ref";
import { useSearchParams, useRouter } from "next/navigation";
import { getAlocacoesList } from "@/service/alocacoes-service";
import { AlocacaoListItem } from "@/data/model/Alocacao";

function AlocacoesPageList() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const page = parseInt(searchParams.get("page") || "1");
  const pageSize = parseInt(searchParams.get("pageSize") || "10");
  const search = searchParams.get("search") || "";

  const [registros, setRegistros] = useState<AlocacaoListItem[]>([]);
  const [isLoading, setIsLoading] = useState<Boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  const getAlocacoes = useCallback(
    async (filterValue: string = "") => {
      setIsLoading(true);
      setError(null);

      try {
        const result = await getAlocacoesList(page, pageSize, filterValue);

        if (!result) {
          setRegistros([]);
          setTotalCount(0);
          return;
        }

        setRegistros(result.data);
        setTotalCount(result.count);
      } catch (err: any) {
        console.error("Erro ao buscar dados:", err);
        setError(err.message);
        setRegistros([]);
        setTotalCount(0);
      } finally {
        setIsLoading(false);
      }
    },
    [page, pageSize, search]
  );

  useEffect(() => {
    getAlocacoes(search);
  }, [page, pageSize, search, getAlocacoes]);

  const onPaginationChange = (newPage: number, newPageSize: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("page", newPage.toString());
    params.set("pageSize", newPageSize.toString());
    router.push(`?${params.toString()}`);
  };

  const onSearchChange = (value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    if (value && value !== "") {
      if (value !== search) {
        params.set("page", "1");
      }
    }
    params.set("search", value);
    router.push(`?${params.toString()}`);
  };

  if (error) {
    return (
      <div className="text-red-500 p-4">Erro ao carregar dados: {error}</div>
    );
  }

  return (
    <>
      {isLoading ? (
        <Skeleton className="h-2/3 rounded-sm" />
      ) : (
        <DataTable
          showToolbar={true}
          data={registros}
          columns={columns}
          page={page}
          pageSize={pageSize}
          totalCount={totalCount}
          onPaginationChange={onPaginationChange}
          onSearchChange={onSearchChange}
          search={search}
        />
      )}
    </>
  );
}

export default AlocacoesPageList;
