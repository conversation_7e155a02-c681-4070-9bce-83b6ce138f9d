'use client';

import { ColumnDef } from '@tanstack/react-table';

import { DataTableColumnHeader } from './data-table-column-header';
import { DataTableRowActions } from './data-table-row-actions';
import { ImportedFile } from '../data/schema';
import { format } from 'date-fns';

export const columns: ColumnDef<ImportedFile>[] = [
  {
    id: 'actions',
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
  {
    accessorKey: 'numero_controle',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Número' />
    ),
    cell: ({ row }) => (
      <div className='w-[80px]'>{row.getValue('numero_controle')}</div>
    ),
  },
  {
    accessorKey: 'created_at',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Criação' />
    ),
    cell: ({ getValue }) => {
      const dateValue = getValue() as Date;
      return (
        <div className='w-[80px]'>{format(dateValue, 'dd/MM/yyyy HH:mm')}</div>
      );
    },
  },
  {
    accessorKey: 'rua',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Rua' />
    ),
    cell: ({ row }) => {
      return (
        <div className='flex space-x-2'>
          <span className='max-w-[500px] truncate font-medium'>
            {row.getValue('rua')}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: 'bloco',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Bloco' />
    ),
    cell: ({ row }) => {
      return (
        <div className='flex space-x-2'>
          <span className='max-w-[500px] truncate font-medium'>
            {row.getValue('bloco')}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: 'baia',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Baia' />
    ),
    cell: ({ row }) => {
      return (
        <div className='flex space-x-2'>
          <span className='max-w-[500px] truncate font-medium'>
            {row.getValue('baia')}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: 'user_email',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Usuário' />
    ),
    cell: ({ row }) => {
      return (
        <div className='flex space-x-2'>
          <span className='max-w-[500px] truncate font-medium'>
            {row.getValue('user_email')}
          </span>
        </div>
      );
    },
  },
];
