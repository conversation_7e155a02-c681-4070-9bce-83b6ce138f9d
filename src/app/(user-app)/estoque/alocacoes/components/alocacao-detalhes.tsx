"use client";

import React, { useEffect, useState } from "react";
import { z } from "zod";
import { AlocacaoItem, Baia, Bloco, Rua } from "@/data/model/Alocacao";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { CalendarIcon, CaretSortIcon, CheckIcon } from "@radix-ui/react-icons";
import { Calendar } from "@/components/ui/calendar";
import { DataTable } from "./data-table";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { columnsItemsAlocacao } from "./columns-items-alocacao";
import { useForm } from "react-hook-form";
import { AlocacaoTypeSchema } from "../data/schema";

type AlocacaoFormValues = z.infer<typeof AlocacaoTypeSchema>;

function AlocacaoDetalhesPage({
  initialData,
  itemsAlocacao,
}: {
  initialData?: any | null;
  itemsAlocacao?: AlocacaoItem[] | null;
}) {
  const form = useForm<AlocacaoFormValues>({
    resolver: zodResolver(AlocacaoTypeSchema),
    defaultValues: initialData,
    mode: "onChange",
  });

  async function onSubmit(data: any) {}

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <FormField
            control={form.control}
            name="numero_controle"
            render={({ field }) => (
              <FormItem>
                <FormLabel>NÚMERO DE CONTROLE</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="user_email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Usuário</FormLabel>
                <FormControl>
                  <Input placeholder="" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="created_at"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Data de Criação</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-[240px] pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground",
                        )}
                      >
                        {field.value
                          ? (
                            format(field.value, "dd/MM/yyyy HH:mm")
                          )
                          : <span>Selecione uma Data</span>}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value ? new Date(field.value) : undefined}
                      onSelect={field.onChange}
                      disabled={(date) =>
                        date > new Date() || date < new Date("1900-01-01")}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </form>
      </Form>
      <div className="mt-6">
        <span className="font-medium text-sm leading-none">
          Itens da Entrada de Estoque
        </span>
        <DataTable
          columns={columnsItemsAlocacao}
          data={itemsAlocacao ? itemsAlocacao : []}
          allowToolbar={false}
        />
      </div>
    </>
  );
}

export default AlocacaoDetalhesPage;
