"use client";

import React, { useEffect, useRef, useState } from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { DataTablePagination } from "./data-table-pagination";
import { useRouter } from "next/navigation";
import { useSearchParams } from "next/navigation";
// import { DataTableToolbarLote } from './data-table-tollbar-lote';
import { createClient } from "../../../../../../utils/supabase/client";

import { z } from "zod";
import {
  Alocacao,
  Baia,
  Bloco,
  LoteAlocacao,
  Rua,
} from "@/data/model/Alocacao";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { CaretSortIcon, CheckIcon } from "@radix-ui/react-icons";
import { cn } from "@/lib/utils";
import { useForm } from "react-hook-form";
import { AlocacaoTypeSchemaNew } from "../data/schema";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  getBaias,
  getBlocos,
  getLotesDesalocados,
  getLotesParaAlocacaoInterna,
  getRuas,
} from "@/service/alocacoes-service";
import { DataTableToolbarLote } from "../../entrada-estoque/components/data-table-tollbar-lote";
import { columnsLoteAlocacao } from "./columns-lote";
import { Skeleton } from "@/components/ui/skeleton";
import { Switch } from "@/components/ui/switch";
import { getLotesParaDesalocacaoExterrna } from "@/service/desalocacoes-service";
import { Lote, LoteMovimentacaoInterna } from "@/data/model/Lote";
import { updateMovimentacaoInterna } from "@/service/movimentacoes-internas-service";

type AlocacaoFormValues = z.infer<typeof AlocacaoTypeSchemaNew>;

export function AlocacaoDetalhesPageNew() {
  const supabase = createClient();
  const [data, setData] = React.useState<LoteAlocacao[] | any[]>([]);
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [saveDisabled, setSaveDisabled] = React.useState(false);
  const [movimentacaoInterna, setMovimentacaoInterna] = React.useState(false);
  const idAlocacaoRef = useRef<string | null>(null);
  //const [tableData, setTableData] = React.useState();
  const router = useRouter();
  const searchParams = useSearchParams();
  const ruaIdParam = searchParams.get("rua_id");
  const blocoIdParam = searchParams.get("bloco_id");
  const baiaIdParam = searchParams.get("baia_id");

  const columns = columnsLoteAlocacao;

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });
  const [ruaList, setRuaList] = useState<Rua[]>([]);
  const [baiaList, setBaiaList] = useState<Baia[]>([]);
  const [blocoList, setBlocoList] = useState<Bloco[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const form = useForm<AlocacaoFormValues>({
    resolver: zodResolver(AlocacaoTypeSchemaNew),
    defaultValues: undefined,
    mode: "onChange",
  });

  const [openRua, setOpenRua] = useState(false);
  const [openBloco, setOpenBloco] = useState(false);
  const [openBaia, setOpenBaia] = useState(false);

  useEffect(() => {
    const getAllData = async () => {
      const ruas = await getRuas().catch((error) => {
        toast({
          duration: 2000,
          variant: "destructive",
          title: "Erro: Alocação de Estoque",
          description: (
            <>
              <p>Erro ao carregar Ruas.</p>
              <p>{error.message}</p>
            </>
          ),
        });
      });
      setRuaList(ruas ? ruas : []);

      const baias = await getBaias().catch((error) => {
        toast({
          duration: 2000,
          variant: "destructive",
          title: "Erro: Alocação de Estoque",
          description: (
            <>
              <p>Erro ao carregar Baias.</p>
              <p>{error.message}</p>
            </>
          ),
        });
      });
      setBaiaList(baias ? baias : []);

      const blocos = await getBlocos().catch((error) => {
        toast({
          duration: 2000,
          variant: "destructive",
          title: "Erro: Alocação de Estoque",
          description: (
            <>
              <p>Erro ao carregar Blocos.</p>
              <p>{error.message}</p>
            </>
          ),
        });
      });
      setBlocoList(blocos ? blocos : []);

      const lotes = await getLotesDesalocados().catch((error) => {
        toast({
          duration: 2000,
          variant: "destructive",
          title: "Erro: Alocação de Estoque",
          description: (
            <>
              <p>Erro ao carregar Lotes.</p>
              <p>{error.message}</p>
            </>
          ),
        });
      });
      setData(lotes ? lotes : []);

      // Pré-preencher os campos se os parâmetros estiverem presentes na URL
      // Isso não afeta o comportamento normal do formulário
      if (ruaIdParam) {
        form.setValue("rua_id", ruaIdParam);
      }

      if (blocoIdParam) {
        form.setValue("bloco_id", blocoIdParam);
      }

      if (baiaIdParam) {
        form.setValue("baia_id", baiaIdParam);
      }
    };

    getAllData().finally(() => setIsLoading(false));
  }, []);

  async function getAlocacaoId(alocacao: Alocacao): Promise<string | null> {
    try {
      const { data, error } = await supabase
        .from("alocacoes")
        .insert(alocacao)
        .select();

      if (!error && data) {
        return data[0].id;
      }
    } catch (error) {
      console.error("Erro ao inserir Alocação de Estoque no Supabase:", error);
    }

    return null;
  }

  async function insertAlocacao(itemsAlocacao: any[]) {
    // Insere itens da nova alocacao
    try {
      const { data, error } = await supabase
        .from("itens_alocacao")
        .insert(itemsAlocacao)
        .select();

      if (!error && data) {
        setSaveDisabled(false);
        toast({
          duration: 3000,
          title: "Alocação de Estoque",
          description: <p>Alocação de Estoque gravada com sucesso!</p>,
        });
        router.push("/estoque/alocacoes");
      }
    } catch (error) {
      setSaveDisabled(false);
      console.error(
        "Erro ao inserir Itens de Alocação de Estoque no Supabase:",
        error
      );
    }
  }

  async function onSubmit(data: any) {
    const listAlocacaoItem: any[] = [];
    const idAlocacao = await getAlocacaoId(data);
    idAlocacaoRef.current = idAlocacao;

    if (table.getSelectedRowModel().rows.length <= 0) {
      toast({
        variant: "destructive",
        duration: 2000,
        title: "Alerta: Alocação de Estoque",
        description: <p>Você precisa selecionar pelo menos um lote!</p>,
      });
      return;
    }

    setSaveDisabled(true);

    // cria lista de itens_alocacao
    table.getSelectedRowModel().rows.map((row) => {
      const loteAlocacao = row.original as Lote;
      listAlocacaoItem.push({
        lote_id: loteAlocacao.id,
        alocacao_id: idAlocacao,
      });
    });

    if (movimentacaoInterna) {
      const listMovimentacaoInternaItem: any[] = [];

      table.getSelectedRowModel().rows.map((row) => {
        const loteMovimentacao = row.original as LoteMovimentacaoInterna;

        const exists = listMovimentacaoInternaItem.some(
          (item) =>
            item.movimentacao_interna_item ===
            loteMovimentacao.movimentacao_interna_id
        );

        if (!exists) {
          listMovimentacaoInternaItem.push({
            movimentacao_interna_item: loteMovimentacao.movimentacao_interna_id,
            alocacao_id: idAlocacaoRef.current,
          });
        }
      });

      listMovimentacaoInternaItem.forEach(async (i) => {
        const createdMovimentacaoInterna = await updateMovimentacaoInterna(
          i.movimentacao_interna_item,
          i.alocacao_id
        );

        if (createdMovimentacaoInterna) {
          //Algo aqui
        }
      });
    }

    await insertAlocacao(listAlocacaoItem);
  }

  useEffect(() => {
    if (movimentacaoInterna) {
      const getLotesDisponivel = async () => {
        const lotesMovimentacaoInterna = await getLotesParaAlocacaoInterna();

        if (lotesMovimentacaoInterna) {
          setData(lotesMovimentacaoInterna);
        }
      };

      getLotesDisponivel().catch(() => {
        console.error("Erro ao obter dados do Supabase.");
      });
    } else {
      const getAllData = async () => {
        const lotes = await getLotesDesalocados().catch((error) => {
          toast({
            duration: 2000,
            variant: "destructive",
            title: "Erro: Alocação de Estoque",
            description: (
              <>
                <p>Erro ao carregar Lotes.</p>
                <p>{error.message}</p>
              </>
            ),
          });
        });
        setData(lotes ? lotes : []);
      };

      getAllData().finally(() => setIsLoading(false));
    }
  }, [movimentacaoInterna]);

  function onMovimentacaoInternaChanged() {
    setMovimentacaoInterna(!movimentacaoInterna);
  }

  return (
    <>
      {isLoading ? (
        <div className="flex flex-col space-y-3">
          <div className="space-y-2">
            <Skeleton className="h-8 w-1/2" />
            <Skeleton className="h-8 w-1/2" />
          </div>
          <Skeleton className="h-[200px] w-1/2 rounded-xl" />
        </div>
      ) : (
        <div>
          <div className="flex items-center space-x-2 mb-6">
            <Switch
              checked={movimentacaoInterna}
              onCheckedChange={onMovimentacaoInternaChanged}
            />
            <span>Movimentação Interna</span>
          </div>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="rua_id"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Rua</FormLabel>
                    <Popover open={openRua} onOpenChange={setOpenRua}>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            role="combobox"
                            className={cn(
                              "w-[240px] justify-between",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value
                              ? ruaList.find((rua) => rua.id === field.value)
                                  ?.nome
                              : "Selecione a Rua"}
                            <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-[240px] p-0">
                        <Command>
                          <CommandInput
                            placeholder="Pesquisar..."
                            className="h-9"
                          />
                          <CommandList>
                            <CommandEmpty>Sem Registros.</CommandEmpty>
                            <CommandGroup>
                              {ruaList.map((rua) => (
                                <CommandItem
                                  value={rua.nome}
                                  key={rua.id}
                                  onSelect={() => {
                                    form.setValue("rua_id", rua.id);
                                    setOpenRua(false);
                                  }}
                                >
                                  {rua.nome}
                                  <CheckIcon
                                    className={cn(
                                      "ml-auto h-4 w-4",
                                      rua.id === field.value
                                        ? "opacity-100"
                                        : "opacity-0"
                                    )}
                                  />
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="bloco_id"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Bloco</FormLabel>
                    <Popover open={openBloco} onOpenChange={setOpenBloco}>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            role="combobox"
                            className={cn(
                              "w-[240px] justify-between",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value
                              ? blocoList.find(
                                  (bloco) => bloco.id === field.value
                                )?.nome
                              : "Selecione a Bloco"}
                            <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-[240px] p-0">
                        <Command>
                          <CommandInput
                            placeholder="Pesquisar..."
                            className="h-9"
                          />
                          <CommandList>
                            <CommandEmpty>Sem Registros.</CommandEmpty>
                            <CommandGroup>
                              {blocoList.map((bloco) => (
                                <CommandItem
                                  value={bloco.nome}
                                  key={bloco.id}
                                  onSelect={() => {
                                    form.setValue("bloco_id", bloco.id);
                                    setOpenBloco(false);
                                  }}
                                >
                                  {bloco.nome}
                                  <CheckIcon
                                    className={cn(
                                      "ml-auto h-4 w-4",
                                      bloco.id === field.value
                                        ? "opacity-100"
                                        : "opacity-0"
                                    )}
                                  />
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="baia_id"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Baia</FormLabel>
                    <Popover open={openBaia} onOpenChange={setOpenBaia}>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            role="combobox"
                            className={cn(
                              "w-[240px] justify-between",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value
                              ? baiaList.find((baia) => baia.id === field.value)
                                  ?.nome
                              : "Selecione a Baia"}
                            <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-[240px] p-0">
                        <Command>
                          <CommandInput
                            placeholder="Pesquisar..."
                            className="h-9"
                          />
                          <CommandList>
                            <CommandEmpty>Sem Registros.</CommandEmpty>
                            <CommandGroup>
                              {baiaList.map((baia) => (
                                <CommandItem
                                  value={baia.nome}
                                  key={baia.id}
                                  onSelect={() => {
                                    form.setValue("baia_id", baia.id);
                                    setOpenBaia(false);
                                  }}
                                >
                                  {baia.nome}
                                  <CheckIcon
                                    className={cn(
                                      "ml-auto h-4 w-4",
                                      baia.id === field.value
                                        ? "opacity-100"
                                        : "opacity-0"
                                    )}
                                  />
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-4">
                <DataTableToolbarLote table={table} />
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      {table.getHeaderGroups().map((headerGroup) => (
                        <TableRow key={headerGroup.id}>
                          {headerGroup.headers.map((header) => {
                            return (
                              <TableHead
                                key={header.id}
                                colSpan={header.colSpan}
                              >
                                {header.isPlaceholder
                                  ? null
                                  : flexRender(
                                      header.column.columnDef.header,
                                      header.getContext()
                                    )}
                              </TableHead>
                            );
                          })}
                        </TableRow>
                      ))}
                    </TableHeader>
                    <TableBody>
                      {table.getRowModel().rows?.length ? (
                        table.getRowModel().rows.map((row) => (
                          <TableRow
                            key={row.id}
                            data-state={row.getIsSelected() && "selected"}
                          >
                            {row.getVisibleCells().map((cell) => (
                              <TableCell key={cell.id}>
                                {flexRender(
                                  cell.column.columnDef.cell,
                                  cell.getContext()
                                )}
                              </TableCell>
                            ))}
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell
                            colSpan={columns.length}
                            className="h-24 text-center"
                          >
                            Nenhum resultado.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
                <DataTablePagination table={table} />

                {/* <Button
                onClick={handleSave}
                variant='outline'
                disabled={saveDisabled}
              >
                Salvar
              </Button> */}
              </div>

              <Button type="submit" disabled={saveDisabled} variant="outline">
                Salvar
              </Button>
            </form>
          </Form>
        </div>
      )}
    </>
  );
}
