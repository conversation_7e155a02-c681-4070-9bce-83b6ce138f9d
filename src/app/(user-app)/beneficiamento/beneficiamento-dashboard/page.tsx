import MetabaseDashboardEmbed from '@/components/metabase-dashboard-embed';
import React from 'react'

function BeneficiamentoDashboardPage() {
  const dashboardId = Number(process.env.NEXT_PUBLIC_DASHBOARD_BENEFICIAMENTO_ID);

  return (
    <>
       {dashboardId ? <MetabaseDashboardEmbed dashboardId={dashboardId} /> : <p>ID do Dashboard não informado.</p>}
    </>
  );
}

export default BeneficiamentoDashboardPage
