'use client';

import { ColumnDef } from '@tanstack/react-table';

import { Badge } from '@/components/ui/badge';
import { ImportedFile } from '../data/schema';
import { labels, priorities, statuses } from '../data/data';
import { DataTableColumnHeader } from './data-table-column-header';
import { DataTableRowActions } from './data-table-row-actions';

export const columns: ColumnDef<ImportedFile>[] = [
  {
    id: 'actions',
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
  {
    accessorKey: 'created_at',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Criação' />
    ),
    cell: ({ row }) => <div className='w-[80px]'>{row.getValue('created_at')}</div>,
  },
  {
    accessorKey: 'nome_arquivo',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Arquivo' />
    ),
    cell: ({ row }) => {

      return (
        <div className='flex space-x-2'>
          <span className='max-w-[500px] truncate font-medium'>
            {row.getValue('nome_arquivo')}
          </span>
        </div>
      );
    },
  },
];
