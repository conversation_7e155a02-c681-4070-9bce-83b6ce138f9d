import { Metadata } from 'next';
import { z } from 'zod';

import { getImportedFiles } from '@/service/files-service';

import { importedFileSchema } from './data/schema';
import { columns } from './components/columns';
import { DataTable } from './components/data-table';

export const metadata: Metadata = {
  title: 'Ordens de Colheita',
  description: 'Listagem de Ordens de Colheita',
};

function formatDate(createdAt: string): string {
  const date = new Date(createdAt);
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  }).format(date);
}

async function getImportedFilesFromSupabase() {
  const importedFilesList = await getImportedFiles(
    'importacoes_ordens_colheita'
  );

  importedFilesList?.map((i) => {
    i.created_at = formatDate(i.created_at);
  });

  if (importedFilesList) {
    return z.array(importedFileSchema).parse(importedFilesList);
  }
}

async function HarvestFilesUplodedPage() {
  const importedFilesFromDatabase = await getImportedFilesFromSupabase();

  return (
    <>
      <div className='hidden h-full flex-1 flex-col space-y-8 p-8 md:flex'>
        <div className='flex items-center justify-between space-y-2'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>
              Arquivos de Importação de Colheita
            </h2>
          </div>
        </div>
        <DataTable data={importedFilesFromDatabase ?? []} columns={columns} />
      </div>
    </>
  );
}

export default HarvestFilesUplodedPage;
