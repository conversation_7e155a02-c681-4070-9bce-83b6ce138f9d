"use server";

import { createClient } from "../../../../../../../utils/supabase/server";
import { columns } from "./columns";
import { DataTable } from "./data-table";
import { OrdemColheita } from "@/data/model/OrdemColheita";

interface props {
  params: {
    file_id: string;
  };
}

async function getData(file_id: string): Promise<OrdemColheita[]> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from("lotes_ordem_colheita")
      .select(`
      parcelas ( ncc, linhagem, experimento, tag_rfid ),
      numero_lote_colheita,
      importacoes_ordem_colheita_id,
      confirmado
    `)
      .eq("importacoes_ordem_colheita_id", file_id);

    if (!error && data) {
      const parcelas: OrdemColheita[] = data.map((item: any) => ({
        ncc: item.parcelas.ncc,
        linhagem: item.parcelas.linhagem,
        experimento: item.parcelas.experimento,
        tag_rfid: item.parcelas.tag_rfid,
        numero_lote_colheita: item.numero_lote_colheita,
        importacao_ordem_colheita_id: item.importacoes_ordem_colheita_id,
        confirmado: item.confirmado,
      }));

      return parcelas;
    }
  } catch (error) {
    console.error("Erro ao buscar dados no Supabase:", error);
  }

  return [];
}

async function HarvestOrderPage({ params }: props) {
  const data = await getData(params.file_id);

  return (
    <div className="container mx-auto py-10">
      <DataTable columns={columns} data={data} />
    </div>
  );
}

export default HarvestOrderPage;
