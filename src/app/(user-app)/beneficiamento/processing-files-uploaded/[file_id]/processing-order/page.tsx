"use server";

import React from 'react'
import { OrdemBeneficiamento } from '@/data/model/OrdemBeneficiamento';
import { DataTable } from './data-table';
import { columns } from './columns';
import { createClient } from '../../../../../../../utils/supabase/server';

interface props {
  params: {
    file_id: string;
  };
}

async function getData(file_id: string): Promise<OrdemBeneficiamento[]> {

  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from("lotes_ordem_beneficiamento")
      .select(`
        parcelas ( ncc, linhagem, tag_rfid ),
        peso_parcela,
        cultura,
        importacao_ordem_beneficiamento_id,
        lotes( numero_lote )
      `)
      .eq('importacao_ordem_beneficiamento_id', file_id)

    if (!error && data) {
      const parcelas: OrdemBeneficiamento[] = data.map((item: any) => ({
        ncc: item.parcelas.ncc,
        linhagem: item.parcelas.linhagem,
        tag_rfid: item.parcelas.tag_rfid,
        numero_lote: item.lotes.numero_lote,
        importacao_ordem_beneficiamento_id: item.importacao_ordem_beneficiamento_id,
        peso_parcela: item.peso_parcela,
        cultura: item.cultura
      }))

      return parcelas;
    }
  } catch (error) {
    console.error("Erro ao buscar dados no Supabase:", error);
  }

  return [];
}

async function ProcessingOrderPage({ params }: props) {
  const data = await getData(params.file_id);

  return (
    <div className="container mx-auto py-10">
      <DataTable columns={columns} data={data} />
    </div>
  );
}

export default ProcessingOrderPage
