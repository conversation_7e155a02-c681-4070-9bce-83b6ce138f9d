//import { getProcessingFilesUploaded } from "@/services/processing-files-uploaded";
import { getProcessingFilesUploaded } from "@/service/processing-files-uploaded";
import { columns } from "./components/columns";
import { DataTable } from "./components/data-table";

export const dynamic = "force-dynamic";

export default async function ProcessingFilesUploaded() {
  const data = await getProcessingFilesUploaded();

  return (
    <div className="hidden h-full flex-1 flex-col space-y-8 p-8 md:flex">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            Arquivos de Beneficiamento
          </h2>
          <p className="text-muted-foreground">
            Aqui você encontra todos os arquivos de beneficiamento enviados!
          </p>
        </div>
      </div>
      <DataTable data={data} columns={columns} />
    </div>
  );
}
