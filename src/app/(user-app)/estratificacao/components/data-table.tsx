"use client";

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  FilterFn,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";
import { format } from "date-fns";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
//import { DataTableToolbar } from './data-table-tollbar';
import { DataTablePagination } from "./data-table-pagination";
import { DataTableToolbarOP } from "./data-table-toolbar";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  showToolbar: boolean;
}

const customGlobalFilterFn: FilterFn<any> = (row, columnId, filterValue) => {
  const value = row.getValue(columnId);

  // Se o valor for null ou undefined, retorna false
  if (value == null) return false;

  // Se o valor for uma data (verificando se é um objeto Date ou string de data ISO)
  if (
    value instanceof Date ||
    (typeof value === "string" && value.match(/^\d{4}-\d{2}-\d{2}/))
  ) {
    try {
      const date = new Date(value);
      const formattedDate = format(date, "dd/MM/yyyy HH:mm:ss");
      return formattedDate.toLowerCase().includes(filterValue.toLowerCase());
    } catch (error) {
      // Se houver erro ao formatar a data, tenta comparar como string normal
      return String(value)
        .toLowerCase()
        .includes(filterValue.toLowerCase());
    }
  }

  // Para outros tipos de valores, converte para string e compara
  return String(value)
    .toLowerCase()
    .includes(filterValue.toLowerCase());
};

export function DataTable<TData, TValue>({
  columns,
  data,
  showToolbar,
}: DataTableProps<TData, TValue>) {
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] = React.useState<
    VisibilityState
  >({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [globalFilter, setGlobalFilter] = React.useState<string>("");
  const [sorting, setSorting] = React.useState<SortingState>([]);

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      globalFilter,
    },
    enableRowSelection: true,
    enableMultiRowSelection: false,
    onRowSelectionChange: (newSelection) => {
      const selectedRowKeys = Object.keys(newSelection);
      const singleSelection = selectedRowKeys.length > 0
        ? { [selectedRowKeys[0]]: true }
        : {};
      setRowSelection(singleSelection);
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    globalFilterFn: customGlobalFilterFn,
  });

  return (
    <div className="space-y-4">
      {showToolbar && <DataTableToolbarOP table={table} />}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} colSpan={header.colSpan}>
                      {header.isPlaceholder ? null : flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length
              ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              )
              : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    Nenhum resultado.
                  </TableCell>
                </TableRow>
              )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination table={table} />
    </div>
  );
}
