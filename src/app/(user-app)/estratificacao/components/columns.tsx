"use client";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Estratificacao } from "@/data/model/Estratificacao";
import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { ArrowUpDown, MoreHorizontal } from "lucide-react";
import { useRouter } from "next/navigation";
import { createClient } from "../../../../../utils/supabase/client";
import { useState } from "react";

function ActionMenu({ id }: { id: string }) {
  const router = useRouter();
  const supabase = createClient();

  const handleViewDetails = () => {
    router.push(`/estratificacao`);
  };

  const handleUpdateStatus = async (estratificacao_id: string) => {
    try {
      const { data, error } = await supabase
        .from("estratificacoes")
        .update({ concluido: true })
        .eq("id", estratificacao_id)
        .select();
      if (!error && data) {
        return data;
      } else {
        throw new Error(error.message);
      }
    } catch (err: any) {
      console.error("Erro inesperado:", err.message);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Abrir Menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Ações</DropdownMenuLabel>
        <DropdownMenuItem onClick={() => handleUpdateStatus(id)}>
          Finalizar
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export const columns: ColumnDef<Estratificacao>[] = [
  {
    id: "actions",
    cell: ({ row }) => {
      const portion = row.original;

      return <ActionMenu id={portion.id} />;
    },
  },
  {
    accessorKey: "numero_controle",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Número de Controle
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: "concluido",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return row.getValue("concluido") ? "Concluído" : "Pendente";
    },
  },
  {
    accessorKey: "created_at",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Início
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ getValue }) => {
      const dateValue = getValue() as Date;
      return format(dateValue, "dd/MM/yyyy HH:mm");
    },
  },
  {
    accessorKey: "updated_at",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Fim
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ getValue }) => {
      const value = getValue();

      if (
        !value ||
        (typeof value !== "string" && typeof value !== "number" &&
          !(value instanceof Date))
      ) {
        return <div className="text-center">-</div>;
      }

      try {
        const dateValue = new Date(value);
        return format(dateValue, "dd/MM/yyyy HH:mm");
      } catch (error) {
        console.error("Erro ao formatar a data:", error);
        return <div className="text-center">-</div>;
      }
    },
  },
  {
    accessorKey: "op",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          OP
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: "email",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          E-mail
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
];
