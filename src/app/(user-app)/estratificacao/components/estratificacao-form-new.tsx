'use client';

import * as React from 'react';
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Lote } from '@/data/model/Lote';
import { toast } from '@/hooks/use-toast';
import { DataTablePagination } from './data-table-pagination';
import { useRouter } from 'next/navigation';
import { DataTableToolbarOP } from './data-table-toolbar';
import { createClient } from '../../../../../utils/supabase/client';
import { OrdemProducao } from '@/data/model/OrdemProducao';

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
}

export function EstratificacaoFormNew<TData, TValue>({
  columns,
  data,
}: DataTableProps<TData, TValue>) {
  const supabase = createClient()
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [saveDisabled, setSaveDisabled] = React.useState(false);
  const router = useRouter();

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  async function inserEstratificacao(opId : string): Promise<string | null> {
    try {
      const newEstratificacao = {"ordem_producao_id" : opId}

      const { data, error } = await supabase
        .from('estratificacoes')
        .insert(newEstratificacao)
        .select();

      if (!error && data) {
        setSaveDisabled(false);

        toast({
          duration: 3000,
          title: 'Seleção',
          description: <p>Seleção gravada com sucesso!</p>,
        });

        router.push('/estratificacao');
      } else {
        setSaveDisabled(false);
        var errorMessage = ""

        if (error.code === '23505') {
          errorMessage = "Já existe uma estratificação para esta OP!"
        } else {
          errorMessage = "Erro ao gravar estratificaÇão: " + error.message
        }

        toast({
          variant: 'destructive',
          duration: 3000,
          title: 'Estratificação',
          description: <p>{errorMessage}</p>,
        });
      }
    } catch (error) {
      setSaveDisabled(false);

      console.error(
        'Erro ao inserir Seleção no Supabase:',
        error
      );
    }

    return null;
  }

  async function handleSave() {

    if (table.getSelectedRowModel().rows.length <= 0) {
      toast({
        variant: 'destructive',
        duration: 2000,
        title: 'Alerta: Seleção',
        description: <p>Você precisa selecionar uma OP!</p>,
      });
      return;
    }

    setSaveDisabled(true);

    const selectedOP = table.getSelectedRowModel().rows.map((row) => {
      const op = row.original as OrdemProducao;
      return op
    });

    await inserEstratificacao(selectedOP[0].id);
  }

  return (
    <div className='space-y-4'>
      <DataTableToolbarOP table={table} />
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} colSpan={header.colSpan}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className='h-24 text-center'
                >
                  Nenhum resultado.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination table={table} />

      <Button onClick={handleSave} variant='outline' disabled={saveDisabled}>
        Salvar
      </Button>
    </div>
  );
}
