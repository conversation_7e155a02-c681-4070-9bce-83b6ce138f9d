'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { CalendarIcon } from '@radix-ui/react-icons';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { columnsEstratificacaoItem } from './columns-estratificacao-item';
import { Estratificacao } from '@/data/model/Estratificacao';
import { DataTable } from './data-table';

const estratificacaoFormSchema = z.object({
  id: z.string().optional(),
  created_at: z.date().optional(),
  email: z.string().optional(),
  numero_controle: z.string().optional(),
  concluido: z.string().optional(),
  op: z.string().optional(),
});

type EstratificacaoFormValues = z.infer<typeof estratificacaoFormSchema>;

export function EstratificacaoFormShow({
  initialData,
  parcelasSelecionadas,
}: {
  initialData: any;
  parcelasSelecionadas: any[] | null;
}) {
  const form = useForm<EstratificacaoFormValues>({
    resolver: zodResolver(estratificacaoFormSchema),
    defaultValues: initialData,
    mode: 'onChange',
  });

  async function onSubmit(data: any) {}

  return (
    <>
      <div className='mt-6'>
        <span className='font-medium text-sm leading-none'>
          Parcelas
        </span>
        <DataTable
          showToolbar={false}
          columns={columnsEstratificacaoItem}
          data={parcelasSelecionadas ? parcelasSelecionadas : []}
        />
      </div>
    </>
  );
}
