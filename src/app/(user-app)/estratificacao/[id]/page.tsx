import { createClient } from '../../../../../utils/supabase/server';
import { EstratificacaoFormShow } from '../components/estratificacao-form-show';
import EstratificacaoNew from '../components/estratificacao-new';

async function getNumeroEstratificacao(selecao_id: string): Promise<any[]> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('estratificacoes')
      .select('*')
      .eq('id', selecao_id)    

    if (!error && data) {
      
      return data
    }

  } catch (error) {
    console.error('Erro ao buscar dados para do Supabase:', error);
  }

  return [];
}

async function getParcelasSelecionadas(estratificacao_id: string): Promise<any[]> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('vw_parcelas_selecionadas')
      .select('*')
      .eq('estratificacao_id', estratificacao_id)    

    if (!error && data) {
      //console.log(JSON.stringify(data, null, 2))

      const formattedData = data.map((item) => ({
        ...item,
        data_hora_leitura: item.data_hora_leitura ? new Date(item.data_hora_leitura) : null, // Converte ou mantém null
      }));

      return formattedData;
    }

  } catch (error) {
    console.error('Erro ao buscar dados para do Supabase:', error);
  }

  return [];
}

export default async function EstratificacaoEditPage({
  params,
}: {
  params: any;
}) {
  const { id } = params;
  const parcelasSelecionadas = await getParcelasSelecionadas(id)
  const estratificacao = await getNumeroEstratificacao(id)

  return (
    <div className='container mx-auto py-4'>
      <h1 className='text-2xl font-bold mb-4'>
      Nova Estratificação
      </h1>
      <EstratificacaoNew />
    </div>
  );
}
