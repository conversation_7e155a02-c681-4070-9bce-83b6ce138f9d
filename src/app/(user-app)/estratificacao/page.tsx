import ButtonLink from '@/components/app-button-link'
import { But<PERSON> } from '@/components/ui/button'
import React from 'react'
import EstratificacaoPageDetail from './components/page-detail'

export default async function EstratificacaoPage() {
  return (
    <>
      <div className='hidden h-full flex-1 flex-col space-y-8 p-8 md:flex'>
        <div className='flex items-center justify-between space-y-2'>
          <div className='flex justify-between w-full'>
            <h2 className='text-2xl font-bold tracking-tight'>
              Estratificações
            </h2>
            <ButtonLink url='estratificacao/new'>
              {' '}
              <Button variant='outline'>Novo</Button>
            </ButtonLink>
          </div>
        </div>
        <EstratificacaoPageDetail />
      </div>
    </>
  )
}
