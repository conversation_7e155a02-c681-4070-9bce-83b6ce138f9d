import { columns } from "./components/columns";
import { DataTable } from "./components/data-table";
import { getExpedicaoFilesUploaded } from "./actions";

export const dynamic = "force-dynamic";

export default async function ExpedicaoFilesUploaded() {
  const data = await getExpedicaoFilesUploaded();

  return (
    <div className="hidden h-full flex-1 flex-col space-y-8 p-8 md:flex">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            Arquivos de Expedição
          </h2>
          <p className="text-muted-foreground">
            Aqui você encontra todos os arquivos de Expedição enviados!
          </p>
        </div>
      </div>
      <DataTable data={data} columns={columns} />
    </div>
  );
}
