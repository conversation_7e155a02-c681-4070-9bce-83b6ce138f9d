"use server";

import React from 'react'
import { columns } from './columns';
import { createClient } from '../../../../../utils/supabase/server';
import { CaixaExpedicao } from '@/data/model/CaixaExpedicao';
import { DataTable } from './data-table';

interface props {
  params: {
    id: string;
  };
}

async function getData(id: string): Promise<CaixaExpedicao[]> {

  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from("caixas_expedicao")
      .select(`
        exped,
        caixa,
        tag_id,
        local,
        ensaio,
        importacao_expedicao_id,
        confirmado
      `)
      .eq('importacao_expedicao_id', id)

    if (!error && data) {
      const caixas: CaixaExpedicao[] = data.map((item: any) => ({
        exped: item.exped,
        caixa: item.caixa,
        tag_id: item.tag_id,
        local: item.local,
        ensaio: item.ensaio,
        importacao_expedicao_id: item.importacao_expedicao_id,
        confirmado: item.confirmado
      }))

      return caixas;
    }
  } catch (error) {
    console.error("Erro ao buscar dados no Supabase:", error);
  }

  return [];
}

async function CaixasImportacaoExpedicaoPage({ params }: props) {
  const data = await getData(params.id);

  return (
    <div className="container mx-auto py-10">
      <DataTable columns={columns} data={data} />
    </div>
  );
}

export default CaixasImportacaoExpedicaoPage
