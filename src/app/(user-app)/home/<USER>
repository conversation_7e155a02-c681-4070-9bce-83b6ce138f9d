import React from 'react';
import { createClient } from '../../../../utils/supabase/server';
import { redirect } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { logout } from '../../logout/actions';
import MetabaseDashboardEmbed from '@/components/metabase-dashboard-embed';

async function UserApp() {
  const dashboardId = Number(process.env.NEXT_PUBLIC_DASHBOARD_HOME_ID);

  return (
    <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
      {dashboardId ? <MetabaseDashboardEmbed dashboardId={dashboardId} /> : <p>ID do Dashboard não informado.</p>}
    </div>
  );
}

export default UserApp;
