import { toast } from '@/hooks/use-toast';
import { OrdenagensForm } from '../components/ordenagens-form';
import { getOrdenagemById } from '../actions';

export default async function OrdenagensEditPage({ params }: { params: any }) {
  const { id } = params;
  let ordenagem: any;

  if (id !== 'new') {
    await getOrdenagemById(id)
      .then((res) => {
        ordenagem = res;
      })
      .catch((error) => {
        toast({
          duration: 2000,
          variant: 'destructive',
          title: 'Erro: Ordenagens',
          description: (
            <>
              <p>Erro ao carregar Ordenagem.</p>
              <p>{error.message}</p>
            </>
          ),
        });
      });
  }

  return (
    <div className='container mx-auto py-4'>
      <h1 className='text-2xl font-bold mb-4'>
        {id === 'new'
          ? 'Nova Ordenagem'
          : `Alterar Ordenagem: ${ordenagem ? ordenagem!.numero_controle: ''}`}
      </h1>
      <OrdenagensForm initialData={ordenagem} />
    </div>
  );
}
