'use client';

import { useEffect, useMemo, useState } from 'react';
import { columns } from './columns';
import { DataTable } from './data-table';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/hooks/use-toast';
import { getOrdenagens } from '../actions';
import { Ordenagem } from '@/data/model/Ordenagem';

export default function OrdenagensPageDetail() {
  const [registros, setRegistros] = useState<Ordenagem[]>([]);
  const [isLoading, setIsLoading] = useState<Boolean>(true);

  function getOrdenagensList() {
    getOrdenagens()
      .then((res) => {
        setRegistros(res ? res : []);
      })
      .catch((error) => {
        toast({
          duration: 2000,
          variant: 'destructive',
          title: 'Erro: Ordenagens de Estoque',
          description: (
            <>
              <p>Erro ao carregar Ordenagens.</p>
              <p>{error.message}</p>
            </>
          ),
        });
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  const onRefresh = () => {
    getOrdenagensList();
  };

  useEffect(() => {
    getOrdenagensList();
  }, []);

  return (
    <>
      {isLoading ? (
        <Skeleton className='h-2/3 rounded-sm' />
      ) : (
        <DataTable
          showToolbar={true}
          data={registros}
          columns={columns({ onRefresh })}
        />
      )}
    </>
  );
}
