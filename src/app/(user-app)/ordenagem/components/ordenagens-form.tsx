"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { useRouter } from "next/navigation";
import { insertOrdenagem, updateOrdenagem } from "../actions";
import { OrdemProducaoSearch } from "./op_search";
import React from "react";
import { getMinMaxNumeroEnvelopeByOrdemProducaoId } from "@/service/parcelas-ordem-producao-service";

const ordenagensFormSchema = z.object({
  id: z.string().optional(),
  numero_caixa: z
    .string({
      required_error: "Informe o número da caixa.",
    })
    .min(1, {
      message: "O número precisa ter no mínimo 1 caractere.",
    }),
  numero_primeiro_envelope: z
    .string({
      required_error: "Informe o número do primeiro envelope.",
    })
    .min(1, {
      message: "O número precisa ter no mínimo 1 caractere.",
    }),
  numero_ultimo_envelope: z
    .string({
      required_error: "Informe o número do último envelope.",
    })
    .min(1, {
      message: "O número precisa ter no mínimo 1 caractere.",
    }),
  ordem_producao_id: z
    .string({
      required_error: "Informe a OP.",
    })
    .min(1, {
      message: "O campo precisa ter no mínimo 1 caractere.",
    }),
});

type OrdenagensFormValues = z.infer<typeof ordenagensFormSchema>;

export function OrdenagensForm({ initialData }: { initialData: any }) {
  const [selectedOP, setSelectedOP] = React.useState(
    initialData?.ordens_producao.op ?? ""
  );

  const handleOPSelect = async (op: string, id: string) => {
    setSelectedOP(op);
    form.setValue("ordem_producao_id", id);

    // Busca min/max envelopes via service
    const minMax = await getMinMaxNumeroEnvelopeByOrdemProducaoId(id);
    if (minMax) {
      form.setValue("numero_primeiro_envelope", minMax.min);
      form.setValue("numero_ultimo_envelope", minMax.max);
    } else {
      form.setValue("numero_primeiro_envelope", "");
      form.setValue("numero_ultimo_envelope", "");
    }
  };

  const router = useRouter();
  const form = useForm<OrdenagensFormValues>({
    resolver: zodResolver(ordenagensFormSchema),
    defaultValues: initialData
      ? initialData
      : {
          numero_caixa: "",
          numero_primeiro_envelope: "",
          numero_ultimo_envelope: "",
        },
    mode: "onChange",
  });

  async function onSubmit(data: OrdenagensFormValues) {
    if (initialData) {
      let result = await updateOrdenagem(data);

      if (result.success == true) {
        toast({
          title: "Ordenagens",
          description: (
            <>
              <p>
                Ordenagem{" "}
                {result && result.success ? result.datares.numero_controle : ""}{" "}
                foi salva com sucesso!
              </p>
            </>
          ),
        });
        router.push("/ordenagem");
      } else {
        toast({
          duration: 2000,
          variant: "destructive",
          title: "Erro: Ordenagens",
          description: (
            <>
              <p>Erro ao gravar.</p>
              <p>{result.message}</p>
            </>
          ),
        });
      }
      // .then((res) => {
      //   toast({
      //     title: "Ordenagens",
      //     description: (
      //       <>
      //         <p>Ordenagem {res!.numero_controle} foi salva com sucesso!</p>
      //       </>
      //     ),
      //   });
      //   router.push("/ordenagem");
      // })
      // .catch((error) => {
      //   toast({
      //     duration: 2000,
      //     variant: "destructive",
      //     title: "Erro: Ordenagens",
      //     description: (
      //       <>
      //         <p>Erro ao gravar.</p>
      //         <p>{error.message}</p>
      //       </>
      //     ),
      //   });
      // })
      // .finally(() => {});
    } else {
      let result = await insertOrdenagem(data);

      if (result.success == true) {
        toast({
          title: "Ordenagens",
          description: (
            <>
              <p>
                Ordenagem{" "}
                {result && result.success ? result.datares.numero_controle : ""}{" "}
                foi salva com sucesso!
              </p>
            </>
          ),
        });
        router.push("/ordenagem");
      } else {
        console.log(result.message);
        toast({
          duration: 2000,
          variant: "destructive",
          title: "Erro: Ordenagens",
          description: (
            <>
              <p>Erro ao gravar.</p>
              <p>{result.message}</p>
            </>
          ),
        });
      }

      // .then((res) => {
      //   toast({
      //     title: "Ordenagens",
      //     description: (
      //       <>
      //         <p>
      //           Ordenagem{" "}
      //           {res && res.success ? res.datares.numero_controle : ""} foi
      //           salva com sucesso!
      //         </p>
      //       </>
      //     ),
      //   });
      //   router.push("/ordenagem");
      // })
      // .catch((error) => {
      //   console.log(error);
      //   toast({
      //     duration: 2000,
      //     variant: "destructive",
      //     title: "Erro: Ordenagens",
      //     description: (
      //       <>
      //         <p>Erro ao gravar.</p>
      //         <p>{error.message}</p>
      //       </>
      //     ),
      //   });
      // })
      // .finally(() => {});
    }
  }

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <FormField
            control={form.control}
            name="numero_caixa"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Caixa</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Informe o Número da Caixa"
                    className="resize-none"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="ordem_producao_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>OP</FormLabel>
                <FormControl>
                  <OrdemProducaoSearch
                    selectedOP={selectedOP}
                    onSelectOP={handleOPSelect}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="numero_primeiro_envelope"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Primeiro Envelope</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Informe o Número do Primeiro Envelope"
                    className="resize-none"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="numero_ultimo_envelope"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Último Envelope</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Informe o Número do Último Envelope"
                    className="resize-none"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button type="submit" variant="outline">
            Salvar
          </Button>
        </form>
      </Form>
    </>
  );
}
