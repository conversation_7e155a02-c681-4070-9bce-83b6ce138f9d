"use server";

import { Ordenagem } from "@/data/model/Ordenagem";
import { createClient } from "../../../../utils/supabase/server";

// implementado pois por padrao produção o Next.js esconde a mensagem original por segurança.
type OperationResponse =
  | { success: true; datares: Ordenagem }
  | { success: false; message: string };

export async function getOrdenagens(): Promise<Ordenagem[] | undefined> {
  const supabase = createClient();

  const { data, error } = await supabase.from("vw_ordenagens").select("*");

  if (!error && data) {
    return data;
  } else {
    throw new Error(error.message);
  }
}

export async function getOrdenagemById(id: string): Promise<any | undefined> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from("ordenagens")
    .select("*, ordens_producao (op)")
    .eq("id", id);
  if (!error && data) {
    return data[0];
  } else {
    throw new Error(error.message);
  }
}

export async function insertOrdenagem(
  ordenagem: any
): Promise<OperationResponse> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from("ordenagens")
    .insert(ordenagem)
    .select();
  if (!error && data) {
    // return data[0];
    // implementado pois por padrao produção o Next.js esconde a mensagem original por segurança.
    return { success: true, datares: data[0] };
  } else {
    // throw new Error(error.message);
    // implementado pois por padrao produção o Next.js esconde a mensagem original por segurança.
    return { success: false, message: error.message };
  }
}

export async function updateOrdenagem(
  ordenagem: any
): Promise<OperationResponse> {
  const supabase = createClient();

  const dataUpdate = {
    numero_caixa: ordenagem.numero_caixa,
    numero_primeiro_envelope: ordenagem.numero_primeiro_envelope,
    numero_ultimo_envelope: ordenagem.numero_ultimo_envelope,
  };

  const { data, error } = await supabase
    .from("ordenagens")
    .update(dataUpdate)
    .eq("id", ordenagem.id)
    .select();
  if (!error && data) {
    // return data[0];
    // implementado pois por padrao produção o Next.js esconde a mensagem original por segurança.
    return { success: true, datares: data[0] };
  } else {
    // throw new Error(error.message);
    // implementado pois por padrao produção o Next.js esconde a mensagem original por segurança.
    return { success: false, message: error.message };
  }
}
