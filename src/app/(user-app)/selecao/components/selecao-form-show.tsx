"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { CalendarIcon } from "@radix-ui/react-icons";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { columnsSelecaoItem } from "./columns-selecao-item";
import { Selecao } from "@/data/model/Selecao";
import { DataTable } from "./data-table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { PaginationState, SortingState } from "@tanstack/react-table";
import { useCallback, useEffect, useRef, useState } from "react";
import { DataTableOP } from "./data-table-op";

const selecaoFormSchema = z.object({
  id: z.string().optional(),
  created_at: z.union([z.string(), z.date()]).optional(),
  email: z.string().optional(),
  nome_usuario: z.string().optional(),
  numero_controle: z.string().optional(),
  op: z.string().optional(),
  op_descricao: z.string().optional(),
});

type SelecaoFormValues = z.infer<typeof selecaoFormSchema>;

interface SelecaoFormShowProps {
  parcelasSelecionadas: any[];
  initialData: any[];
  totalCount: number;
  currentPage: number;
  pageSize: number;
  sortField: string;
  sortDirection: string;
  filter: string | Record<string, string>;
  selecaoId: string;
}

export function SelecaoFormShow({
  parcelasSelecionadas,
  initialData,
  totalCount,
  currentPage,
  pageSize,
  sortField,
  sortDirection,
  filter,
  selecaoId,
}: SelecaoFormShowProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const isFirstRender = useRef(true);
  const [isLoading, setIsLoading] = useState(false);

  // Log para depuração
  console.log("SelecaoFormShow - initialData recebido:", initialData);

  // Extrair os filtros iniciais do parâmetro filter
  const [initialFilters, setInitialFilters] = useState<Record<string, string>>(
    {},
  );

  // Processar os filtros iniciais quando o componente for montado ou quando filter mudar
  useEffect(() => {
    if (typeof filter === "object" && Object.keys(filter).length > 0) {
      setInitialFilters(filter as Record<string, string>);
    } else if (typeof filter === "string" && filter.trim() !== "") {
      try {
        const parsedFilter = JSON.parse(filter);
        if (typeof parsedFilter === "object") {
          setInitialFilters(parsedFilter);
        }
      } catch (error) {
        // Se não for um JSON válido, não faz nada
        console.log("Filtro não é um JSON válido:", filter);
      }
    }
  }, [filter]);

  const form = useForm<SelecaoFormValues>({
    resolver: zodResolver(selecaoFormSchema),
    defaultValues: initialData?.[0] || {},
    mode: "onChange",
  });

  const selecaoData = initialData?.[0] || {};

  // Formatar a data de criação
  const formattedDate = selecaoData?.created_at
    ? (() => {
      try {
        // Tentar converter para Date se for uma string
        const date = typeof selecaoData.created_at === "string"
          ? new Date(selecaoData.created_at)
          : selecaoData.created_at;
        return format(date, "dd/MM/yyyy HH:mm");
      } catch (error) {
        console.error("Erro ao formatar data:", error);
        return "Não disponível";
      }
    })()
    : "Não disponível";

  // Calcular o número total de páginas
  const pageCount = Math.ceil(totalCount / pageSize);

  // Estado para controlar a paginação, ordenação e filtros
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: currentPage,
    pageSize: pageSize,
  });

  const [sorting, setSorting] = useState<SortingState>([
    {
      id: sortField,
      desc: sortDirection === "desc",
    },
  ]);

  // Inicializar o globalFilter com uma string vazia ou com o valor de filter se for uma string
  const [globalFilter, setGlobalFilter] = useState<string>(
    typeof filter === "string" ? filter : "",
  );

  // Função para criar a URL com os parâmetros atualizados
  const createUrl = useCallback((params: URLSearchParams) => {
    return `${pathname}?${params.toString()}`;
  }, [pathname]);

  // Atualizar a URL quando a paginação, ordenação ou filtros mudarem
  useEffect(() => {
    // Pular a primeira renderização para evitar atualizações desnecessárias
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    // Verificar se os parâmetros realmente mudaram para evitar loops infinitos
    const currentPage = searchParams.get("page");
    const currentPageSize = searchParams.get("pageSize");
    const currentSortField = searchParams.get("sortField");
    const currentSortDirection = searchParams.get("sortDirection");
    const currentFilter = searchParams.get("filter");

    const newPage = String(pagination.pageIndex + 1); // Ajuste para 1-indexed para URL
    const newPageSize = String(pagination.pageSize);
    const newSortField = sorting.length > 0 ? sorting[0].id : sortField;
    const newSortDirection = sorting.length > 0
      ? (sorting[0].desc ? "desc" : "asc")
      : sortDirection;

    // Verificar se o filtro atual é um objeto JSON
    let isCurrentFilterObject = false;
    if (currentFilter) {
      try {
        const parsedFilter = JSON.parse(currentFilter);
        isCurrentFilterObject = typeof parsedFilter === "object";
      } catch (error) {
        // Se não for um JSON válido, não é um objeto
        isCurrentFilterObject = false;
      }
    }

    // Só atualizar a URL se algum parâmetro realmente mudou
    // Ignorar mudanças de filtro quando já temos um filtro avançado (objeto) aplicado
    // e estamos tentando aplicar um filtro global (string)
    const shouldUpdateUrl = currentPage !== newPage ||
      currentPageSize !== newPageSize ||
      currentSortField !== newSortField ||
      currentSortDirection !== newSortDirection ||
      (currentFilter !== globalFilter &&
        !(isCurrentFilterObject && globalFilter));

    if (shouldUpdateUrl) {
      setIsLoading(true);
      const params = new URLSearchParams(searchParams.toString());

      // Adicionar parâmetros de paginação
      params.set("page", newPage);
      params.set("pageSize", newPageSize);

      // Adicionar parâmetros de ordenação
      if (sorting.length > 0) {
        params.set("sortField", newSortField);
        params.set("sortDirection", newSortDirection);
      }

      // Adicionar parâmetro de filtro
      if (globalFilter && globalFilter.trim() !== "") {
        params.set("filter", globalFilter);
      } else if (!isCurrentFilterObject) {
        // Só remover o filtro se não for um filtro avançado (objeto)
        params.delete("filter");
      }

      // Atualizar a URL sem recarregar a página
      router.push(createUrl(params), { scroll: false });
    }
  }, [
    pagination,
    sorting,
    globalFilter,
    pathname,
    router,
    searchParams,
    createUrl,
    sortField,
    sortDirection,
  ]);

  // Desativar o estado de carregamento quando os dados forem atualizados
  useEffect(() => {
    setIsLoading(false);
  }, [parcelasSelecionadas]);

  // Handlers para mudanças na paginação, ordenação e filtros
  const handlePaginationChange = (newPagination: PaginationState) => {
    setPagination(newPagination);
  };

  const handleSortingChange = (newSorting: SortingState) => {
    setSorting(newSorting);
  };

  const handleGlobalFilterChange = (newFilter: string) => {
    console.log("Filtro alterado (detalhe):", newFilter); // Log para debug

    // Atualizar o estado apenas se o valor for diferente
    if (newFilter !== globalFilter) {
      setGlobalFilter(newFilter);
      // Resetar para a primeira página quando o filtro mudar
      setPagination((prev) => ({ ...prev, pageIndex: 0 }));
    }
  };

  // Handler para filtros avançados
  const handleAdvancedFiltersChange = (filters: Record<string, string>) => {
    console.log("SelecaoFormShow - Recebendo filtros avançados:", filters);

    // Limpar filtro global quando usar filtros avançados
    if (Object.keys(filters).length > 0 && globalFilter) {
      setGlobalFilter("");
    }

    // Atualizar a URL com os novos parâmetros
    const params = new URLSearchParams(searchParams.toString());

    // Adicionar filtros à URL como JSON string
    if (Object.keys(filters).length > 0) {
      params.set("filter", JSON.stringify(filters));
    } else {
      params.delete("filter");
    }

    // Resetar para primeira página
    params.set("page", "1");

    // Atualizar URL mantendo outros parâmetros
    const url = createUrl(params);
    router.push(url, { scroll: false });
  };

  // Formatar a exibição do usuário
  const displayUser = selecaoData?.email
    ? selecaoData.nome_usuario
      ? `${selecaoData.nome_usuario} (${selecaoData.email})`
      : selecaoData.email
    : "Não disponível";

  return (
    <>
      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Detalhes da Seleção</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">
                  Número de Controle
                </h3>
                <p className="mt-1 text-lg font-semibold">
                  {selecaoData?.numero_controle || "Não disponível"}
                </p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">
                  Criado por
                </h3>
                <p className="mt-1 text-lg font-semibold">
                  {displayUser}
                </p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">
                  Data de Criação
                </h3>
                <p className="mt-1 text-lg font-semibold">{formattedDate}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Parcelas da Ordem de Produção</CardTitle>
          </CardHeader>
          <CardContent>
            <DataTableOP
              showToolbar={true}
              columns={columnsSelecaoItem}
              data={parcelasSelecionadas ? parcelasSelecionadas : []}
              pageCount={pageCount}
              onPaginationChange={handlePaginationChange}
              onSortingChange={handleSortingChange}
              onGlobalFilterChange={handleGlobalFilterChange}
              onAdvancedFiltersChange={handleAdvancedFiltersChange}
              manualPagination={true}
              manualSorting={true}
              manualFiltering={true}
              isLoading={isLoading}
              initialFilters={initialFilters}
            />
          </CardContent>
        </Card>
      </div>
    </>
  );
}
