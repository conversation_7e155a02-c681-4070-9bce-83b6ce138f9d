"use client";

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  FilterFn,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  OnChangeFn,
  PaginationState,
  SortingState,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";
import { format } from "date-fns";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { DataTablePagination } from "./data-table-pagination";
import { DataTableToolbarOP } from "./data-table-toolbar-op";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  showToolbar?: boolean;
  pageCount?: number;
  onPaginationChange?: (pagination: PaginationState) => void;
  onSortingChange?: (sorting: SortingState) => void;
  onGlobalFilterChange?: (filter: string) => void;
  onAdvancedFiltersChange?: (filters: Record<string, string>) => void;
  manualPagination?: boolean;
  manualSorting?: boolean;
  manualFiltering?: boolean;
  isLoading?: boolean;
  initialFilters?: Record<string, string>;
}

const customGlobalFilterFn: FilterFn<any> = (row, columnId, filterValue) => {
  const value = row.getValue(columnId);

  // Se não houver valor de filtro, retorna true
  if (!filterValue) return true;

  // Se o valor for null ou undefined, retorna false
  if (value == null) return false;

  // Se o valor for uma data (verificando se é um objeto Date ou string de data ISO)
  if (
    value instanceof Date ||
    (typeof value === "string" && value.match(/^\d{4}-\d{2}-\d{2}/))
  ) {
    try {
      const date = new Date(value);
      const formattedDate = format(date, "dd/MM/yyyy HH:mm");
      return formattedDate.toLowerCase().includes(filterValue.toLowerCase());
    } catch (error) {
      // Se houver erro ao formatar a data, tenta comparar como string normal
      return String(value)
        .toLowerCase()
        .includes(filterValue.toLowerCase());
    }
  }

  // Para outros tipos de valores, converte para string e compara
  return String(value)
    .toLowerCase()
    .includes(filterValue.toLowerCase());
};

export function DataTableOP<TData, TValue>({
  columns,
  data,
  showToolbar = true,
  pageCount,
  onPaginationChange,
  onSortingChange,
  onGlobalFilterChange,
  onAdvancedFiltersChange,
  manualPagination = false,
  manualSorting = false,
  manualFiltering = false,
  isLoading = false,
  initialFilters = {},
}: DataTableProps<TData, TValue>) {
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] = React.useState<
    VisibilityState
  >({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [globalFilter, setGlobalFilter] = React.useState<string>("");
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  // Handlers para mudanças de estado
  const handleGlobalFilterChange = React.useCallback(
    (value: string) => {
      console.log("DataTable handleGlobalFilterChange:", value); // Log para debug

      // Atualizar o estado local apenas se o valor for diferente
      if (value !== globalFilter) {
        setGlobalFilter(value);

        // Resetar para a primeira página quando o filtro mudar
        setPagination((prev) => ({ ...prev, pageIndex: 0 }));

        // Chamar o callback externo se existir
        if (onGlobalFilterChange) {
          console.log("Calling onGlobalFilterChange with:", value); // Log para debug
          onGlobalFilterChange(value);
        }
      }
    },
    [onGlobalFilterChange, globalFilter],
  );

  const handleSortingChange: OnChangeFn<SortingState> = React.useCallback(
    (updater) => {
      setSorting(updater);
      // Garantir que temos um array para passar para o callback
      const newSorting = typeof updater === "function"
        ? updater(sorting)
        : updater;
      onSortingChange?.(newSorting);
    },
    [onSortingChange, sorting],
  );

  const handlePaginationChange: OnChangeFn<PaginationState> = React.useCallback(
    (updater) => {
      setPagination(updater);
      // Garantir que temos um objeto para passar para o callback
      const newPagination = typeof updater === "function"
        ? updater(pagination)
        : updater;
      onPaginationChange?.(newPagination);
    },
    [onPaginationChange, pagination],
  );

  const table = useReactTable({
    data,
    columns,
    pageCount: pageCount,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      globalFilter,
      pagination,
    },
    filterFns: {
      custom: customGlobalFilterFn,
    },
    globalFilterFn: customGlobalFilterFn,
    enableGlobalFilter: true,
    manualPagination,
    manualSorting,
    manualFiltering,
    onGlobalFilterChange: handleGlobalFilterChange,
    onSortingChange: handleSortingChange,
    onPaginationChange: handlePaginationChange,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onRowSelectionChange: setRowSelection,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
  });

  return (
    <div className="space-y-4">
      {showToolbar && (
        <DataTableToolbarOP
          table={table}
          isLoading={isLoading}
          onAdvancedFiltersChange={onAdvancedFiltersChange}
          initialFilters={initialFilters}
        />
      )}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder ? null : flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length
              ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              )
              : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    {isLoading ? "Carregando..." : "Nenhum resultado."}
                  </TableCell>
                </TableRow>
              )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination table={table} isLoading={isLoading} />
    </div>
  );
}
