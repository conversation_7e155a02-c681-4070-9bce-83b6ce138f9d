"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import { Table } from "@tanstack/react-table";
import { FilterIcon, X } from "lucide-react";
import { useEffect, useState } from "react";

interface AdvancedFiltersProps<TData> {
  table: Table<TData>;
  isLoading?: boolean;
  onApplyFilters: (filters: Record<string, string>) => void;
}

export function DataTableAdvancedFilters<TData>({
  table,
  isLoading = false,
  onApplyFilters,
}: AdvancedFiltersProps<TData>) {
  // Estado para armazenar os valores dos filtros
  const [filters, setFilters] = useState<Record<string, string>>({
    numero_controle: "",
    email: "",
    op: "",
    created_at: "",
  });

  // Estado para controlar se o popover está aberto
  const [isOpen, setIsOpen] = useState(false);

  // Handler para mudança nos filtros
  const handleFilterChange = (field: string, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handler para limpar todos os filtros
  const handleClearFilters = () => {
    setFilters({
      numero_controle: "",
      email: "",
      op: "",
      created_at: "",
    });
    onApplyFilters({});
  };

  // Handler para aplicar os filtros
  const handleApplyFilters = () => {
    // Filtrar apenas os campos que têm valores
    const activeFilters = Object.entries(filters).reduce(
      (acc, [key, value]) => {
        if (value) {
          acc[key] = value;
        }
        return acc;
      },
      {} as Record<string, string>,
    );

    // Fechar o popover antes de aplicar os filtros
    setIsOpen(false);

    // Aplicar os filtros após um pequeno delay para garantir que o popover seja fechado primeiro
    setTimeout(() => {
      onApplyFilters(activeFilters);
    }, 100);
  };

  // Verificar se há filtros ativos
  const hasActiveFilters = Object.values(filters).some((value) => value);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="h-8 border-dashed"
          disabled={isLoading}
        >
          <FilterIcon className="mr-2 h-4 w-4" />
          Filtros Avançados
          {hasActiveFilters && (
            <span className="ml-2 rounded-full bg-primary text-primary-foreground px-2 py-0.5 text-xs">
              {Object.values(filters).filter(Boolean).length}
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-4" align="start">
        <div className="space-y-4">
          <h4 className="font-medium leading-none">Filtros</h4>
          <Separator />

          <div className="space-y-2">
            <Label htmlFor="numero_controle">Número de Controle</Label>
            <Input
              id="numero_controle"
              placeholder="Filtrar apenas por número de controle"
              value={filters.numero_controle}
              onChange={(e) =>
                handleFilterChange("numero_controle", e.target.value)}
              className="h-8"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">E-mail</Label>
            <Input
              id="email"
              placeholder="Filtrar apenas por e-mail"
              value={filters.email}
              onChange={(e) => handleFilterChange("email", e.target.value)}
              className="h-8"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="op">OP</Label>
            <Input
              id="op"
              placeholder="Filtrar apenas por OP"
              value={filters.op}
              onChange={(e) => handleFilterChange("op", e.target.value)}
              className="h-8"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="created_at">Data</Label>
            <Input
              id="created_at"
              placeholder="dd/MM/yyyy HH:mm"
              value={filters.created_at}
              onChange={(e) => handleFilterChange("created_at", e.target.value)}
              className="h-8"
            />
          </div>

          <div className="flex items-center justify-between pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearFilters}
              disabled={!hasActiveFilters}
            >
              Limpar Filtros
            </Button>
            <Button size="sm" onClick={handleApplyFilters}>
              Aplicar Filtros
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
