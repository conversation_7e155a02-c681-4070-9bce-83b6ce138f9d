"use client";

import { Table } from "@tanstack/react-table";
import { DataTableAdvancedFilters } from "./data-table-advanced-filters";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  isLoading?: boolean;
  onAdvancedFiltersChange?: (filters: Record<string, string>) => void;
}

export function DataTableToolbar<TData>({
  table,
  isLoading = false,
  onAdvancedFiltersChange,
}: DataTableToolbarProps<TData>) {
  // Handler para aplicar filtros avançados
  const handleAdvancedFiltersChange = (filters: Record<string, string>) => {
    console.log("DataTableToolbarOP recebeu filtros avançados:", filters);

    // Chamar o callback externo se existir
    if (onAdvancedFiltersChange) {
      console.log("Chamando onAdvancedFiltersChange com:", filters);
      onAdvancedFiltersChange(filters);
    }
  };

  return (
    <div className="flex flex-col sm:flex-row items-center justify-between gap-4 py-4">
      <div className="flex flex-1 items-center space-x-2">
        <DataTableAdvancedFilters
          table={table}
          isLoading={isLoading}
          onApplyFilters={handleAdvancedFiltersChange}
        />
      </div>
    </div>
  );
}
