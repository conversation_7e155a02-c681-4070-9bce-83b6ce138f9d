"use client";

import React, { useEffect, useRef, useState } from "react";
import { createClient } from "../../../../../utils/supabase/client";
import { Skeleton } from "@/components/ui/skeleton";
import { DataTable } from "./data-table";
import { columns } from "./columns";
import { PaginationState, SortingState } from "@tanstack/react-table";
import { format } from "date-fns";
import { Selecao } from "@/data/model/Selecao";

// Estendendo o tipo Selecao para incluir propriedades opcionais específicas desta página
interface SelecaoWithJoins extends Selecao {
  user_profiles?: {
    email: string;
  };
  ordens_producao?: {
    op: string;
  };
  email?: string;
  op?: string;
}

export default function SelecaoPageDetail() {
  const supabase = createClient();
  const [registros, setRegistros] = useState<SelecaoWithJoins[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [totalCount, setTotalCount] = useState<number>(0);
  const isInitialFetch = useRef(true);

  // Estados para paginação, ordenação e filtros
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [sorting, setSorting] = useState<SortingState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>("");
  const [advancedFilters, setAdvancedFilters] = useState<
    Record<string, string>
  >({});

  // Calcular o número total de páginas
  const pageCount = Math.ceil(totalCount / pagination.pageSize);

  // Função principal para buscar dados
  const fetchData = async () => {
    // Só definir isLoading como true se não for uma operação de filtro
    // ou se for a primeira busca
    if (registros.length === 0) {
      setIsLoading(true);
    } else {
      // Mesmo para operações de filtro, mostrar um indicador de carregamento
      setIsLoading(true);
    }

    try {
      console.log("Iniciando fetchData com filtros:", {
        globalFilter,
        advancedFilters,
        sorting,
      });

      // Construir a consulta base
      let query = supabase
        .from("selecoes")
        .select(
          `
          id, 
          created_at, 
          numero_controle, 
          user_profiles(email),
          ordens_producao(op)
        `,
          { count: "exact" },
        )
        .eq("ativo", true);

      // Aplicar filtros avançados se existirem
      if (Object.keys(advancedFilters).length > 0) {
        console.log("Aplicando filtros avançados:", advancedFilters);

        // Aplicar cada filtro individualmente em sua respectiva coluna
        for (const [field, value] of Object.entries(advancedFilters)) {
          if (value) {
            console.log(`Aplicando filtro para ${field}:`, value);

            if (field === "numero_controle") {
              // Filtrar pelo campo numero_controle
              query = query.filter("numero_controle", "ilike", `%${value}%`);
              console.log("Aplicado filtro para numero_controle:", value);
            } else if (field === "email") {
              // Primeiro, encontrar os user_ids correspondentes ao email
              const { data: userIds } = await supabase
                .from("user_profiles")
                .select("id")
                .ilike("email", `%${value}%`);

              if (userIds && userIds.length > 0) {
                // Se encontrou usuários, filtrar as seleções pelos user_ids encontrados
                query = query.in("user_id", userIds.map((u) => u.id));

                // Executar a consulta para obter a contagem
                const { count, error: countError } = await query;

                if (countError) {
                  console.error(
                    "Erro ao obter contagem com filtro de email:",
                    countError,
                  );
                  setIsLoading(false);
                  return;
                }

                // Atualizar o total de registros
                setTotalCount(count || 0);
              } else {
                // Se não encontrou nenhum usuário com esse email, forçar resultado vazio
                setTotalCount(0); // Define explicitamente a contagem como 0
                setRegistros([]); // Limpa os registros
                setIsLoading(false);
                return; // Retorna imediatamente sem executar mais queries
              }
            } else if (field === "op") {
              // Primeiro, encontrar os op_ids correspondentes
              const { data: opIds } = await supabase
                .from("ordens_producao")
                .select("id")
                .ilike("op", `%${value}%`);

              if (opIds && opIds.length > 0) {
                // Se encontrou OPs, filtrar as seleções pelos op_ids encontrados
                query = query.in("ordem_producao_id", opIds.map((op) => op.id));

                // Executar a consulta para obter a contagem
                const { count, error: countError } = await query;

                if (countError) {
                  console.error(
                    "Erro ao obter contagem com filtro de OP:",
                    countError,
                  );
                  setIsLoading(false);
                  return;
                }

                // Atualizar o total de registros
                setTotalCount(count || 0);
              } else {
                // Se não encontrou nenhuma OP, forçar resultado vazio
                setTotalCount(0);
                setRegistros([]);
                setIsLoading(false);
                return;
              }
            } else if (field === "created_at") {
              // Remove espaços extras e converte para minúsculo para comparação
              const searchTerm = value.trim().toLowerCase();

              // Primeiro, busca a data formatada para comparação
              const { data: records } = await supabase
                .from("selecoes")
                .select(`
                  id,
                  created_at
                `)
                .eq("ativo", true)
                .not("created_at", "is", null);

              if (records && records.length > 0) {
                // Filtra os registros que correspondem ao termo de busca
                const matchingIds = records
                  .filter((record) => {
                    if (!record.created_at) return false;

                    const date = new Date(record.created_at);
                    // Formata a data no padrão brasileiro com hora e minuto
                    const formattedDate = format(date, "dd/MM/yyyy HH:mm");

                    // Verifica se o termo de busca está contido na data formatada
                    return formattedDate.toLowerCase().includes(searchTerm);
                  })
                  .map((record) => record.id);

                if (matchingIds.length > 0) {
                  // Aplica o filtro com os IDs encontrados
                  query = query.in("id", matchingIds);

                  // Executar a consulta para obter a contagem
                  const { count, error: countError } = await query;

                  if (countError) {
                    console.error(
                      "Erro ao obter contagem com filtro de data:",
                      countError,
                    );
                    setIsLoading(false);
                    return;
                  }

                  // Atualizar o total de registros
                  setTotalCount(count || 0);
                } else {
                  // Se não encontrou correspondências
                  setTotalCount(0);
                  setRegistros([]);
                  setIsLoading(false);
                  return;
                }
              } else {
                // Se não houver registros ou ocorrer erro
                setTotalCount(0);
                setRegistros([]);
                setIsLoading(false);
                return;
              }
            }
          }
        }

        // Executar a consulta para obter a contagem
        const { count, error: countError } = await query;

        if (countError) {
          console.error(
            "Erro ao obter contagem com filtros avançados:",
            countError,
          );
          console.error("Query:", query);
          setIsLoading(false);
          return;
        }

        console.log("Contagem de registros com filtros avançados:", count);

        // Atualizar o total de registros
        setTotalCount(count || 0);
      } // Adicionar filtro global se existir e não houver filtros avançados
      else if (globalFilter && globalFilter.trim() !== "") {
        const filterTerm = globalFilter.trim().toLowerCase();
        console.log("Aplicando filtro global no servidor:", filterTerm);

        // Aplicar filtro em campos relevantes usando a sintaxe de filtro do Supabase
        query = query.filter("numero_controle", "ilike", `%${filterTerm}%`);

        // Executar a consulta para obter a contagem
        const { count: count1, error: countError1 } = await query;

        if (countError1) {
          console.error("Erro ao obter contagem 1:", countError1);
          setIsLoading(false);
          return;
        }

        // Se não encontrou resultados, tenta buscar por email
        if (count1 === 0) {
          query = supabase
            .from("selecoes")
            .select(
              `
              id, 
              created_at, 
              numero_controle, 
              user_profiles(email),
              ordens_producao(op)
            `,
              { count: "exact" },
            )
            .eq("ativo", true)
            .filter("user_profiles.email", "ilike", `%${filterTerm}%`);

          // Executar a consulta para obter a contagem
          const { count: count2, error: countError2 } = await query;

          if (countError2) {
            console.error("Erro ao obter contagem 2:", countError2);
            setIsLoading(false);
            return;
          }

          // Se não encontrou resultados, tenta buscar por op
          if (count2 === 0) {
            query = supabase
              .from("selecoes")
              .select(
                `
                id, 
                created_at, 
                numero_controle, 
                user_profiles(email),
                ordens_producao(op)
              `,
                { count: "exact" },
              )
              .eq("ativo", true)
              .filter("ordens_producao.op", "ilike", `%${filterTerm}%`);

            // Executar a consulta para obter a contagem
            const { count, error: countError } = await query;

            if (countError) {
              console.error("Erro ao obter contagem 3:", countError);
              setIsLoading(false);
              return;
            }

            // Atualizar o total de registros
            setTotalCount(count || 0);
          } else {
            // Atualizar o total de registros
            setTotalCount(count2 || 0);
          }
        } else {
          // Atualizar o total de registros
          setTotalCount(count1 || 0);
        }
      } else {
        // Adicionar ordenação
        if (sorting.length > 0) {
          const { id, desc } = sorting[0];

          // Mapear o id da coluna para o nome da coluna no banco de dados
          let orderColumn = id;
          if (id === "email") {
            query = query.order("user_profiles(email)", {
              ascending: !desc,
              nullsFirst: false,
            });
          } else if (id === "op") {
            query = query.order("ordens_producao(op)", {
              ascending: !desc,
              nullsFirst: false,
            });
          } else {
            query = query.order(orderColumn, { ascending: !desc });
          }
        } else {
          // Ordenação padrão
          query = query.order("created_at", { ascending: false });
        }

        // Primeiro, obter a contagem total de registros
        const { count, error: countError } = await query;

        if (countError) {
          console.error("Erro ao obter contagem:", countError);
          setIsLoading(false);
          return;
        }

        // Atualizar o total de registros
        setTotalCount(count || 0);
      }

      // Adicionar ordenação se não estiver filtrando com filtro global
      if (
        !globalFilter || globalFilter.trim() === "" ||
        Object.keys(advancedFilters).length > 0
      ) {
        if (sorting.length > 0) {
          const { id, desc } = sorting[0];

          // Mapear o id da coluna para o nome da coluna no banco de dados
          let orderColumn = id;
          if (id === "email") {
            query = query.order("user_profiles(email)", {
              ascending: !desc,
              nullsFirst: false,
            });
          } else if (id === "op") {
            query = query.order("ordens_producao(op)", {
              ascending: !desc,
              nullsFirst: false,
            });
          } else {
            query = query.order(orderColumn, { ascending: !desc });
          }
        } else {
          // Ordenação padrão
          query = query.order("created_at", { ascending: false });
        }
      }

      // Calcular o range para paginação
      const from = pagination.pageIndex * pagination.pageSize;
      const to = from + pagination.pageSize - 1;

      // Adicionar paginação à consulta
      query = query.range(from, to);

      // Executar a consulta com paginação
      const { data, error } = await query;

      if (error) {
        console.error("Erro ao obter dados:", error);
        setIsLoading(false);
        return;
      }

      // Formatar os dados
      const formattedData = data.map((item: any) => ({
        ...item,
        email: item.user_profiles?.email || "", // Garantir que nunca seja null/undefined
        op: item.ordens_producao?.op || "", // Garantir que nunca seja null/undefined
      }));

      formattedData.forEach((item) => {
        delete item.user_profiles;
        delete item.ordens_producao;
      });

      console.log("Dados formatados após filtro:", formattedData);
      console.log("Total de registros após filtro:", formattedData.length);

      // Atualizar os registros
      setRegistros(formattedData);
    } catch (error) {
      console.error("Erro ao obter dados:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Buscar dados quando qualquer estado mudar
  useEffect(() => {
    console.log("useEffect triggered with:", {
      pageIndex: pagination.pageIndex,
      pageSize: pagination.pageSize,
      sorting,
      globalFilter,
      advancedFilters,
    });

    // Adicionar um pequeno delay para garantir que o estado foi atualizado
    const timer = setTimeout(() => {
      console.log("Chamando fetchData após delay");
      fetchData();
    }, 100);

    return () => clearTimeout(timer);
  }, [
    pagination.pageIndex,
    pagination.pageSize,
    sorting,
    globalFilter,
    advancedFilters,
  ]);

  // Handlers para mudanças na paginação, ordenação e filtros
  const handlePaginationChange = (newPagination: PaginationState) => {
    setPagination(newPagination);
  };

  const handleSortingChange = (newSorting: SortingState) => {
    setSorting(newSorting);
    // Resetar para a primeira página quando a ordenação mudar
    setPagination((prev) => ({ ...prev, pageIndex: 0 }));
  };

  const handleGlobalFilterChange = (newFilter: string) => {
    console.log("PageDetail handleGlobalFilterChange:", newFilter); // Log para debug

    // Limpar filtros avançados quando usar a busca rápida
    if (newFilter && Object.keys(advancedFilters).length > 0) {
      setAdvancedFilters({});
    }

    // Atualizar o estado apenas se o valor for diferente
    if (newFilter !== globalFilter) {
      console.log("Updating globalFilter state to:", newFilter); // Log para debug

      // Não definir isLoading como true aqui para não bloquear a interface
      // enquanto o usuário está digitando

      setGlobalFilter(newFilter);
      // Resetar para a primeira página quando o filtro mudar
      setPagination((prev) => ({ ...prev, pageIndex: 0 }));
    }
  };

  // Handler para filtros avançados
  const handleAdvancedFiltersChange = (filters: Record<string, string>) => {
    console.log("Advanced filters changed:", filters);

    // Limpar filtro global quando usar filtros avançados
    if (Object.keys(filters).length > 0 && globalFilter) {
      console.log(
        "Limpando filtro global porque filtros avançados foram aplicados",
      );
      setGlobalFilter("");
    }

    // Verificar se os filtros realmente mudaram
    const filtersChanged =
      JSON.stringify(filters) !== JSON.stringify(advancedFilters);
    console.log("Filtros avançados mudaram?", filtersChanged);

    if (filtersChanged) {
      // Atualizar os filtros avançados
      console.log("Atualizando filtros avançados para:", filters);
      setAdvancedFilters(filters);

      // Resetar para a primeira página quando os filtros mudarem
      setPagination((prev) => ({ ...prev, pageIndex: 0 }));
    }
  };

  return (
    <>
      <DataTable
        showToolbar={true}
        data={registros}
        columns={columns}
        pageCount={pageCount}
        onPaginationChange={handlePaginationChange}
        onSortingChange={handleSortingChange}
        onGlobalFilterChange={handleGlobalFilterChange}
        onAdvancedFiltersChange={handleAdvancedFiltersChange}
        manualPagination={true}
        manualSorting={true}
        manualFiltering={true}
        isLoading={isLoading}
      />
    </>
  );
}
