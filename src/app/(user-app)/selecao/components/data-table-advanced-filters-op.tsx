"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import { Table } from "@tanstack/react-table";
import { FilterIcon } from "lucide-react";
import { useEffect, useState } from "react";

interface AdvancedFiltersProps<TData> {
  table: Table<TData>;
  isLoading?: boolean;
  onApplyFilters: (filters: Record<string, string>) => void;
  initialFilters?: Record<string, string>; // Nova prop para receber filtros iniciais
}

export function DataTableAdvancedFiltersOP<TData>({
  table,
  isLoading = false,
  onApplyFilters,
  initialFilters = {},
}: AdvancedFiltersProps<TData>) {
  // Estado para armazenar os valores dos filtros
  // Importante: os nomes dos campos devem corresponder exatamente aos nomes das colunas na tabela vw_parcelas_selecionadas
  const [filters, setFilters] = useState<Record<string, string>>({
    op: initialFilters.op || "",
    ncc: initialFilters.ncc || "",
    data_hora_leitura: initialFilters.data_hora_leitura || "",
  });

  // Estado para controlar se o popover está aberto
  const [isOpen, setIsOpen] = useState(false);

  // Atualizar os filtros quando as props mudarem
  useEffect(() => {
    if (initialFilters && Object.keys(initialFilters).length > 0) {
      setFilters({
        op: initialFilters.op || "",
        ncc: initialFilters.ncc || "",
        data_hora_leitura: initialFilters.data_hora_leitura || "",
      });
    }
  }, [initialFilters]);

  // Handler para mudança nos filtros
  const handleFilterChange = (field: string, value: string) => {
    console.log(`Alterando filtro ${field} para:`, value);
    setFilters((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handler para limpar todos os filtros
  const handleClearFilters = () => {
    setFilters({
      op: "",
      ncc: "",
      data_hora_leitura: "",
    });
    onApplyFilters({});
  };

  // Handler para aplicar os filtros
  const handleApplyFilters = () => {
    // Filtrar apenas os campos que têm valores
    const activeFilters = Object.entries(filters).reduce(
      (acc, [key, value]) => {
        if (value) {
          acc[key] = value;
        }
        return acc;
      },
      {} as Record<string, string>,
    );

    console.log("Aplicando filtros:", activeFilters);

    // Fechar o popover antes de aplicar os filtros
    setIsOpen(false);

    // Aplicar os filtros após um pequeno delay para garantir que o popover seja fechado primeiro
    setTimeout(() => {
      onApplyFilters(activeFilters);
      console.log("Filtros aplicados:", activeFilters);
    }, 100);
  };

  // Verificar se há filtros ativos
  const hasActiveFilters = Object.values(filters).some((value) => value);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="h-8 border-dashed"
          disabled={isLoading}
        >
          <FilterIcon className="mr-2 h-4 w-4" />
          Filtros Avançados
          {hasActiveFilters && (
            <span className="ml-2 rounded-full bg-primary text-primary-foreground px-2 py-0.5 text-xs">
              {Object.values(filters).filter(Boolean).length}
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-4" align="start">
        <div className="space-y-4">
          <h4 className="font-medium leading-none">Filtros</h4>
          <Separator />

          <div className="space-y-2">
            <Label htmlFor="op">Ordem de Produção</Label>
            <Input
              id="op"
              placeholder="Filtrar por OP"
              value={filters.op}
              onChange={(e) => handleFilterChange("op", e.target.value)}
              className="h-8"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="ncc">NCC</Label>
            <Input
              id="ncc"
              placeholder="Filtrar por NCC"
              value={filters.ncc}
              onChange={(e) => handleFilterChange("ncc", e.target.value)}
              className="h-8"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="data_hora_leitura">Leitura</Label>
            <Input
              id="data_hora_leitura"
              placeholder="dd/MM/yyyy HH:mm"
              value={filters.data_hora_leitura}
              onChange={(e) =>
                handleFilterChange("data_hora_leitura", e.target.value)}
              className="h-8"
            />
          </div>

          <div className="flex items-center justify-between pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearFilters}
              disabled={!hasActiveFilters}
            >
              Limpar Filtros
            </Button>
            <Button size="sm" onClick={handleApplyFilters}>
              Aplicar Filtros
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
