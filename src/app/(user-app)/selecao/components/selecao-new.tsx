'use client';

import React, { useEffect, useState } from 'react';
import { createClient } from '../../../../../utils/supabase/client';
import { Skeleton } from '@/components/ui/skeleton';
import { SelecaoFormNew } from './selecao-form-new';
import { columnsOP } from './columns-op';

export default function SelecaoNew() {
  const supabase = createClient()
  const [registros, setRegistros] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<Boolean>(true);

  useEffect(() => {
    const getOPs = async () => {
      const { data, error } = await supabase
        .from('ordens_producao')
        .select(
          `
            id,
            op
          `
        )
        .eq('ativo', true);

      if (!error && data) {
        setRegistros(data);
        setIsLoading(false);
      }
    };

    getOPs().catch(() => {
      setIsLoading(false);
      console.error('Erro ao obter dados do Supabase.');
    });
  }, []);

  return (
    <>
      {isLoading ? (
        <Skeleton className='h-60 rounded-sm' />
      ) : (
        <SelecaoFormNew data={registros} columns={columnsOP} />
      )}
    </>
  );
}
