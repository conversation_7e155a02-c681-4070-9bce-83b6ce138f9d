import ButtonLink from '@/components/app-button-link'
import { Button } from '@/components/ui/button'
import React from 'react'
import SelecaoPageDetail from './components/page-detail'

export default async function SelecaoPage() {
  return (
    <>
      <div className='hidden h-full flex-1 flex-col space-y-8 p-8 md:flex'>
        <div className='flex items-center justify-between space-y-2'>
          <div className='flex justify-between w-full'>
            <h2 className='text-2xl font-bold tracking-tight'>
              Seleções
            </h2>
            <ButtonLink url='selecao/new'>
              {' '}
              <Button variant='outline'>Novo</Button>
            </ButtonLink>
          </div>
        </div>
        <SelecaoPageDetail />
      </div>
    </>
  )
}
