import { createClient } from "../../../../../utils/supabase/server";
import { SelecaoFormShow } from "../components/selecao-form-show";
import SelecaoNew from "../components/selecao-new";

async function getNumeroSelecao(selecao_id: string): Promise<any[]> {
  const supabase = createClient();

  try {
    console.log("Buscando seleção com ID:", selecao_id);

    // Buscar a seleção sem joins primeiro para verificar os dados
    const { data: selecaoData, error: selecaoError } = await supabase
      .from("selecoes")
      .select("*")
      .eq("id", selecao_id);

    if (selecaoError) {
      console.error("Erro ao buscar dados básicos da seleção:", selecaoError);
      return [];
    }

    if (!selecaoData || selecaoData.length === 0) {
      console.error("Seleção não encontrada:", selecao_id);
      return [];
    }

    console.log("Dados da seleção encontrados:", selecaoData[0]);

    // Extrair os IDs para buscar dados relacionados
    const selecao = selecaoData[0];
    const userId = selecao.user_id;
    const opId = selecao.ordem_producao_id;

    console.log("IDs extraídos - userId:", userId, "opId:", opId);

    // Buscar dados do usuário se existir
    let userData = null;
    if (userId) {
      const { data: user, error: userError } = await supabase
        .from("user_profiles")
        .select("*")
        .eq("id", userId)
        .single();

      if (userError) {
        console.error("Erro ao buscar dados do usuário:", userError);
      } else if (user) {
        userData = user;
        console.log("Dados do usuário encontrados:", userData);
      }
    }

    // Buscar dados da ordem de produção se existir
    let opData = null;
    if (opId) {
      const { data: op, error: opError } = await supabase
        .from("ordens_producao")
        .select("*")
        .eq("id", opId)
        .single();

      if (opError) {
        console.error("Erro ao buscar dados da ordem de produção:", opError);
      } else if (op) {
        opData = op;
        console.log("Dados da ordem de produção encontrados:", opData);
      }
    }

    // Formatar os dados para facilitar o acesso no componente
    const formattedData = {
      ...selecao,
      email: userData?.email || null,
      nome_usuario: userData?.nome || null,
      op: opData?.op || null,
      op_descricao: opData?.descricao || null,
    };

    console.log("Dados formatados para o componente:", formattedData);

    return [formattedData];
  } catch (error) {
    console.error("Erro ao buscar dados da seleção:", error);
    return [];
  }
}

async function getParcelasSelecionadas(
  selecao_id: string,
  page = 0,
  pageSize = 10,
  sortField = "ncc",
  sortDirection = "asc",
  filter: string | Record<string, string> = "",
): Promise<{ data: any[]; count: number }> {
  const supabase = createClient();
  console.log("getParcelasSelecionadas - Iniciando busca com filtros:", filter);

  try {
    let query = supabase
      .from("vw_parcelas_selecionadas")
      .select("*", { count: "exact" })
      .eq("selecao_id", selecao_id);

    // Se filter for um objeto (filtros avançados)
    if (typeof filter === "object" && Object.keys(filter).length > 0) {
      console.log("Aplicando filtros avançados:", filter);
      Object.entries(filter).forEach(([key, value]) => {
        if (value && value.trim() !== "") {
          const filterValue = value.trim().toLowerCase();
          query = query.filter(key, "ilike", `%${filterValue}%`);
          console.log(`Aplicando filtro: ${key} ilike %${filterValue}%`);
        }
      });
    } // Se filter for uma string (filtro global)
    else if (typeof filter === "string" && filter.trim() !== "") {
      const filterTerm = filter.trim().toLowerCase();
      console.log("Aplicando filtro global:", filterTerm);
      query = query.or(
        `ncc.ilike.%${filterTerm}%,descricao.ilike.%${filterTerm}%`,
      );
    }

    // Adicionar ordenação
    query = query.order(sortField, { ascending: sortDirection === "asc" });

    // Adicionar paginação
    const from = page * pageSize;
    const to = from + pageSize - 1;
    query = query.range(from, to);

    // Executar a consulta
    const { data, count, error } = await query;

    if (error) {
      console.error("Erro na consulta:", error);
      return { data: [], count: 0 };
    }

    console.log(`Encontrados ${count} registros`);
    return {
      data: data || [],
      count: count || 0,
    };
  } catch (error) {
    console.error("Erro ao buscar dados:", error);
    return { data: [], count: 0 };
  }
}

export default async function SelecaoEditPage({
  params,
  searchParams,
}: {
  params: { id: string };
  searchParams: {
    page?: string;
    pageSize?: string;
    sortField?: string;
    sortDirection?: string;
    filter?: string;
  };
}) {
  console.log("SelecaoEditPage - searchParams recebidos:", searchParams);

  const { id } = params;
  const page = searchParams.page ? parseInt(searchParams.page) - 1 : 0;
  const pageSize = searchParams.pageSize ? parseInt(searchParams.pageSize) : 10;
  const sortField = searchParams.sortField || "ncc";
  const sortDirection = searchParams.sortDirection || "asc";

  let filter: string | Record<string, string> = "";
  try {
    if (searchParams.filter) {
      const parsedFilter = JSON.parse(searchParams.filter);
      filter = typeof parsedFilter === "object"
        ? parsedFilter
        : searchParams.filter;
      console.log("Filtro processado:", filter);
    }
  } catch (error) {
    filter = searchParams.filter || "";
    console.log("Usando filtro como string:", filter);
  }

  // Buscar os dados da seleção
  const selecaoData = await getNumeroSelecao(id);
  console.log("Dados da seleção:", selecaoData);

  // Buscar as parcelas selecionadas
  const { data: parcelasSelecionadas, count } = await getParcelasSelecionadas(
    id,
    page,
    pageSize,
    sortField,
    sortDirection,
    filter,
  );

  const selecao = await getNumeroSelecao(id)
  const numero_selecao = selecao?.[0]?.numero_controle ?? "Indefinido";

  return (
    <div className='container mx-auto py-4'>
      <h1 className='text-2xl font-bold mb-4'>
        {id === 'new'
          ? 'Nova Seleção'
          : `Seleção: ${numero_selecao}`}
      </h1>
      {
        id === 'new' ? (
        <SelecaoNew />
      ) : (
        <SelecaoFormShow
          parcelasSelecionadas={parcelasSelecionadas}
          initialData={selecaoData} // Passar os dados da seleção
          totalCount={count}
          currentPage={page}
          pageSize={pageSize}
          sortField={sortField}
          sortDirection={sortDirection}
          filter={filter}
          selecaoId={id}
        />
      )}
    </div>
  );
}
