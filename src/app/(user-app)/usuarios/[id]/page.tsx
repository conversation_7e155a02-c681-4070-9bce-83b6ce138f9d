"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useRouter, useParams } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { useState, useEffect } from "react";
import { getUserById, updateUser } from "../actions";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const availableRoutes = [
  { id: "home", descricao: "Home/Dashboard" },
  { id: "file-upload", descricao: "Upload de Arquivos/Documentos" },
  { id: "beneficiamento", descricao: "Beneficiamento" },
  { id: "estoque", descricao: "Estoque" },
  { id: "selecao", descricao: "Seleção" },
  { id: "estratificacao", descricao: "Estratificação" },
  { id: "contagem", descricao: "Contagem" },
  { id: "ordenagem", descricao: "Ordenagem" },
  { id: "expedicao", descricao: "Expedição" },
  { id: "bi", descricao: "BI/Dashboards de BI" },
  { id: "configuracoes", descricao: "Configurações" },
  // { id: "perfil", descricao: "Perfil" },
  // { id: "usuarios", descricao: "Usuários" },
];

export default function UsuarioPage() {
  const router = useRouter();
  const params = useParams();
  const { toast } = useToast();
  const { isAdmin, isLoading } = useAuth();
  const [selectedRoutes, setSelectedRoutes] = useState<string[]>([]);
  const [loading, setLoading] = useState(params.id !== "novo");
  const isNovo = params.id === "novo";
  const [currentRole, setCurrentRole] = useState<string>("user");

  // Schema dinâmico conforme isNovo
  const formSchema = isNovo
    ? z.object({
        email: z.string().email("Email inválido"),
        password: z.string().min(6, "A senha deve ter no mínimo 6 caracteres"),
        permittedRoutes: z.array(z.string()).optional(),
        role: z.enum(["user", "admin"]).default("user"),
      })
    : z.object({
        email: z.string().email("Email inválido"),
        password: z.string().optional(),
        permittedRoutes: z.array(z.string()).optional(),
        role: z.enum(["user", "admin"]).default("user"),
      });

  type FormValues = z.infer<typeof formSchema>;

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
      permittedRoutes: [],
      role: "user",
    },
  });

  useEffect(() => {
    if (!isNovo) {
      async function fetchUser() {
        setLoading(true);
        const user = await getUserById(params.id as string);
        if (user) {
          form.setValue("email", user.email);
          setSelectedRoutes(user.permitted_routes || []);
          form.setValue("permittedRoutes", user.permitted_routes || []);
          const userRole = user.role || "user";
          form.setValue("role", userRole as "user" | "admin");
          setCurrentRole(userRole);
        }
        setLoading(false);
      }
      fetchUser();
    }
  }, [params.id, isNovo, form]);

  useEffect(() => {
    form.setValue("permittedRoutes", selectedRoutes);
  }, [selectedRoutes]);

  function toggleRoute(route: string) {
    setSelectedRoutes((prev) =>
      prev.includes(route) ? prev.filter((r) => r !== route) : [...prev, route]
    );
  }

  // Função para lidar com a mudança de role
  const handleRoleChange = (value: string) => {
    form.setValue("role", value as "user" | "admin");
    setCurrentRole(value);

    // Se a role for admin, limpa as rotas selecionadas
    if (value === "admin") {
      setSelectedRoutes([]);
      form.setValue("permittedRoutes", []);
    }
  };

  async function onSubmit(data: FormValues) {
    try {
      // Validação para garantir que pelo menos uma rota esteja selecionada (apenas para usuários comuns)
      if (
        data.role === "user" &&
        (!data.permittedRoutes || data.permittedRoutes.length === 0)
      ) {
        toast({
          variant: "destructive",
          title: "Erro",
          description: "Selecione pelo menos uma rota permitida para o usuário",
        });
        return;
      }

      let result;
      if (isNovo) {
        // Garante que password é string e nunca undefined
        if (!data.password) {
          toast({
            variant: "destructive",
            title: "Erro",
            description: "Senha é obrigatória para novo usuário",
          });
          return;
        }
        // Remove o campo password caso esteja undefined (por segurança de tipagem)
        const { password, ...rest } = data;
        result = await import("../actions").then((mod) =>
          mod.createUser({ ...rest, password: data.password! })
        );
      } else {
        // Não envie o campo password para updateUser
        const { password, ...rest } = data;
        result = await updateUser(params.id as string, rest);
      }
      if (result.success) {
        toast({
          title: "Sucesso",
          description: isNovo
            ? "Usuário criado com sucesso"
            : "Usuário atualizado com sucesso",
        });
        router.push("/usuarios");
        router.refresh();
      } else {
        toast({
          variant: "destructive",
          title: "Erro",
          description:
            result.error ||
            (isNovo ? "Erro ao criar usuário" : "Erro ao atualizar usuário"),
        });
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Erro",
        description:
          (isNovo ? "Erro ao criar usuário " : "Erro ao atualizar usuário ") +
          error,
      });
    }
  }

  if (isLoading || loading) {
    return <div>Carregando...</div>;
  }

  return (
    <div className="container flex items-center justify-center py-10">
      <Card className="w-2/3">
        <CardHeader>
          <CardTitle>{isNovo ? "Novo Usuário" : "Editar Usuário"}</CardTitle>
          <CardDescription>
            {isNovo
              ? "Crie um novo usuário para acessar o sistema"
              : "Altere os dados do usuário"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        value={field.value ?? ""}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        ref={field.ref}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {isNovo && (
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Senha</FormLabel>
                      <FormControl>
                        <Input type="password" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
              {isAdmin && (
                <>
                  <FormField
                    control={form.control}
                    name="role"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Perfil</FormLabel>
                        <Select
                          onValueChange={(value) => handleRoleChange(value)}
                          defaultValue={field.value}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Selecione o perfil" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="user">Usuário</SelectItem>
                            <SelectItem value="admin">Administrador</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormItem>
                    <FormLabel>Permissões de Rotas</FormLabel>
                    <div className="grid grid-cols-2 gap-2 max-h-48 overflow-auto border rounded p-2">
                      {availableRoutes.map((route) => (
                        <label
                          key={route.id}
                          className={`flex items-center space-x-2 ${
                            currentRole === "admin" ? "opacity-50" : ""
                          }`}
                        >
                          <input
                            type="checkbox"
                            checked={selectedRoutes.includes(route.id)}
                            onChange={() => toggleRoute(route.id)}
                            disabled={currentRole === "admin"}
                            className="cursor-pointer"
                          />
                          <span>{route.descricao}</span>
                        </label>
                      ))}
                    </div>
                    {currentRole === "admin" && (
                      <p className="text-sm text-muted-foreground mt-1">
                        Administradores têm acesso a todas as rotas
                        automaticamente
                      </p>
                    )}
                  </FormItem>
                </>
              )}
              <Button type="submit" className="w-full">
                {isNovo ? "Criar Usuário" : "Salvar Alterações"}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
