import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import Link from "next/link";
import { columns } from "./components/columns";
import { DataTable } from "./components/data-table";
import { createClient } from "../../../../utils/supabase/server";

export const metadata = {
  title: "Usuários",
  description: "Gerenciamento de usuários do sistema",
};

interface User {
  id: string;
  email: string;
  created_at: string;
}

export default async function UsuariosPage() {
  const supabase = createClient();

  const { data: users, error } = await supabase
    .from("user_profiles")
    .select("id, email, created_at")
    .order("created_at", { ascending: false });

  if (error) {
    console.error("Erro ao buscar usuários:", error);
    return <div>Erro ao carregar usuários.</div>;
  }

  return (
    <div className="flex flex-col gap-4 p-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Usuários</h1>
          <p className="text-muted-foreground">
            Gerencie os usuários do sistema
          </p>
        </div>
        <Button asChild>
          <Link href="/usuarios/novo">
            <Plus className="mr-2 h-4 w-4" />
            Novo Usuário
          </Link>
        </Button>
      </div>
      <DataTable columns={columns} data={users} />
    </div>
  );
}
