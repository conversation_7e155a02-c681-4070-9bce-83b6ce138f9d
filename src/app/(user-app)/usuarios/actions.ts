"use server";

import { createClient } from "../../../../utils/supabase/server";
import { createAdminClient } from "../../../../utils/supabase/admin";
import { revalidatePath } from "next/cache";

interface CreateUserData {
  email: string;
  password: string;
  permittedRoutes?: string[];
  role?: string;
}

export async function createUser(data: CreateUserData) {
  const supabase = createClient();
  const adminClient = createAdminClient();

  try {
    // Primeiro, cria o usuário na autenticação do Supabase
    const { data: authData, error: authError } =
      await adminClient.auth.admin.createUser({
        email: data.email,
        password: data.password,
        email_confirm: true,
      });

    if (authError) throw authError;

    // Aguarda até que o registro em user_profiles exista (timeout de até 2 segundos)
    const userId = authData.user.id;
    const maxAttempts = 10;
    const delayMs = 200;
    let profileExists = false;
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      const { data: profileData, error: selectError } = await supabase
        .from("user_profiles")
        .select("id")
        .eq("id", userId)
        .maybeSingle();

      if (selectError) {
        throw selectError;
      }

      if (profileData && profileData.id) {
        profileExists = true;
        break;
      }
      // Aguarda antes de tentar novamente
      await new Promise((resolve) => setTimeout(resolve, delayMs));
    }

    if (!profileExists) {
      throw new Error(
        "Registro em user_profiles não foi criado após 2 segundos."
      );
    }

    // Agora faz o update de permitted_routes
    const { error: profileError } = await adminClient
      .from("user_profiles")
      .update({
        email: data.email,
        permitted_routes: data.permittedRoutes || [],
        role: data.role || "user",
      })
      .eq("id", userId);

    if (profileError) throw profileError;

    revalidatePath("/usuarios");
    return { success: true };
  } catch (error: any) {
    console.error("Erro ao criar usuário:", error);
    return { success: false, error: error.message };
  }
}

export async function getUserById(id: string) {
  const adminClient = createAdminClient();
  const { data, error } = await adminClient
    .from("user_profiles")
    .select("id, email, permitted_routes, role")
    .eq("id", id)
    .single();
  if (error) {
    console.error("Erro ao buscar usuário:", error);
    return null;
  }
  return data;
}

export async function updateUser(
  id: string,
  data: { email: string; permittedRoutes?: string[]; role?: string }
) {
  const adminClient = createAdminClient();
  try {
    const { error } = await adminClient
      .from("user_profiles")
      .update({
        email: data.email,
        permitted_routes: data.permittedRoutes || [],
        role: data.role || "user",
      })
      .eq("id", id);

    if (error) throw error;

    revalidatePath("/usuarios");
    return { success: true };
  } catch (error: any) {
    console.error("Erro ao atualizar usuário:", error);
    return { success: false, error: error.message };
  }
}
