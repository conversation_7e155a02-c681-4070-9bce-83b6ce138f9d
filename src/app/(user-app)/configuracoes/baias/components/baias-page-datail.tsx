'use client';

import { useEffect, useMemo, useState } from 'react';
import { columns } from './columns';
import { DataTable } from './data-table';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/hooks/use-toast';
import { getBaias } from '@/service/alocacoes-service';
import { Baia } from '@/data/model/Alocacao';

export default function BaiasPageDetail() {
  const [registros, setRegistros] = useState<Baia[]>([]);
  const [isLoading, setIsLoading] = useState<Boolean>(true);

  function getBaiasList() {
    getBaias()
      .then((res) => {
        setRegistros(res ? res : []);
      })
      .catch((error) => {
        toast({
          duration: 2000,
          variant: 'destructive',
          title: 'Erro: Baias de Estoque',
          description: (
            <>
              <p>Erro ao carregar Baias.</p>
              <p>{error.message}</p>
            </>
          ),
        });
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  const onRefresh = () => {
    getBaiasList();
  };

  useEffect(() => {
    getBaiasList();
  }, []);

  return (
    <>
      {isLoading ? (
        <Skeleton className='h-2/3 rounded-sm' />
      ) : (
        <DataTable
          showToolbar={true}
          data={registros}
          columns={columns({ onRefresh })}
        />
      )}
    </>
  );
}
