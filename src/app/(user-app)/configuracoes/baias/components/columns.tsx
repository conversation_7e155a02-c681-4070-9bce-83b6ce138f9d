'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { <PERSON>a } from '@/data/model/Alocacao';
import { toast } from '@/hooks/use-toast';
import { deleteBaia } from '@/service/alocacoes-service';
import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { ArrowUpDown, MoreHorizontal } from 'lucide-react';
import { useRouter } from 'next/navigation';

function ActionMenu({
  id,
  onRefresh,
}: {
  id: string;
  onRefresh: (value: any) => void;
}) {
  const router = useRouter();

  const handleViewDetails = () => {
    router.push(`baias/${id}`);
  };

  const handleDelete = async (res: any) => {
    await deleteBaia(id)
      .then((res) => {
        toast({
          title: 'Baias do Estoque',
          description: (
            <>
              <p>Baia {res?.nome} foi excluído com sucesso!</p>
            </>
          ),
        });
        onRefresh(res);
      })
      .catch((error) => {
        toast({
          duration: 2000,
          variant: 'destructive',
          title: 'Erro: Baias do Estoque',
          description: (
            <>
              <p>Erro ao excluir Baia.</p>
              <p>{error.message}</p>
            </>
          ),
        });
      })
      .finally(() => {});
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant='ghost' className='h-8 w-8 p-0'>
          <span className='sr-only'>Abrir Menu</span>
          <MoreHorizontal className='h-4 w-4' />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end'>
        <DropdownMenuLabel>Ações</DropdownMenuLabel>
        <DropdownMenuItem onClick={handleViewDetails}>
          Visualizar/Alterar Registro
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleDelete}>
          Excluir Registro
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export const columns = ({
  onRefresh,
}: {
  onRefresh: (reg: any) => void;
}): ColumnDef<Baia>[] => [
  {
    id: 'actions',
    cell: ({ row }) => {
      const portion = row.original;

      return <ActionMenu id={portion.id} onRefresh={onRefresh} />;
    },
  },
  {
    accessorKey: 'nome',
    header: ({ column }) => {
      return (
        <Button
          variant='ghost'
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Nome
          <ArrowUpDown className='ml-2 h-4 w-4' />
        </Button>
      );
    },
  },
  {
    accessorKey: 'created_at',
    header: ({ column }) => {
      return (
        <Button
          variant='ghost'
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Data
          <ArrowUpDown className='ml-2 h-4 w-4' />
        </Button>
      );
    },
    cell: ({ getValue }) => {
      const dateValue = getValue() as Date;
      return format(dateValue, 'dd/MM/yyyy HH:mm');
    },
  },
];
