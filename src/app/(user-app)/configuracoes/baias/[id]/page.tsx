import { toast } from '@/hooks/use-toast';
import { BaiasForm } from '../components/baias-form';
import { getBaiasById } from '@/service/alocacoes-service';

export default async function BaiasEditPage({ params }: { params: any }) {
  const { id } = params;
  let baia: any;

  if (id !== 'new') {
    await getBaiasById(id)
      .then((res) => {
        baia = res;
      })
      .catch((error) => {
        toast({
          duration: 2000,
          variant: 'destructive',
          title: 'Erro: Baias do Estoque',
          description: (
            <>
              <p>Erro ao carregar Baias do Estoque.</p>
              <p>{error.message}</p>
            </>
          ),
        });
      });
  }

  return (
    <div className='container mx-auto py-4'>
      <h1 className='text-2xl font-bold mb-4'>
        {id === 'new'
          ? 'Nova Baia do Estoque'
          : `Alterar Baia do Estoque: ${baia ? baia!.nome : ''}`}
      </h1>
      <BaiasForm initialData={baia} />
    </div>
  );
}
