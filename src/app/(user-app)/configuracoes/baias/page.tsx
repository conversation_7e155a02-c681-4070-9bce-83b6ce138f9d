import { Metada<PERSON> } from 'next';
import { But<PERSON> } from '@/components/ui/button';
import ButtonLink from '@/components/app-button-link';
import BaiasPageDetail from './components/baias-page-datail';

export const metadata: Metadata = {
  title: 'Baias do Estoque',
  description: 'Listagem de Baias do Estoque',
};

export default async function BaiasPage() {
  return (
    <>
      <div className='hidden h-full flex-1 flex-col space-y-8 p-8 md:flex'>
        <div className='flex items-center justify-between space-y-2'>
          <div className='flex justify-between w-full'>
            <h2 className='text-2xl font-bold tracking-tight'>
              Baias do Estoque
            </h2>
            <ButtonLink url='baias/new'>
              {' '}
              <Button variant='outline'>Novo</Button>
            </ButtonLink>
          </div>
        </div>
        <BaiasPageDetail />
      </div>
    </>
  );
}
