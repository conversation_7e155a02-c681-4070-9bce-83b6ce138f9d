import { toast } from '@/hooks/use-toast';
import { RuasForm } from '../components/ruas-form';
import { getRuasById } from '@/service/alocacoes-service';

export default async function RuasEditPage({ params }: { params: any }) {
  const { id } = params;
  let rua: any;

  if (id !== 'new') {
    await getRuasById(id)
      .then((res) => {
        rua = res;
      })
      .catch((error) => {
        toast({
          duration: 2000,
          variant: 'destructive',
          title: 'Erro: Ruas do Estoque',
          description: (
            <>
              <p>Erro ao carregar Ruas do Estoque.</p>
              <p>{error.message}</p>
            </>
          ),
        });
      });
  }

  return (
    <div className='container mx-auto py-4'>
      <h1 className='text-2xl font-bold mb-4'>
        {id === 'new'
          ? 'Nova Rua do Estoque'
          : `Alterar Rua do Estoque: ${rua ? rua!.nome : ''}`}
      </h1>
      <RuasForm initialData={rua} />
    </div>
  );
}
