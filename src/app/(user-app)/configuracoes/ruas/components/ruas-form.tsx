'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { toast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import { insertRua, updateRua } from '@/service/alocacoes-service';
import { useRouter } from 'next/navigation';

const ruasFormSchema = z.object({
  id: z.string().optional(),
  nome: z
    .string({
      required_error: 'Informe um Nome.',
    })
    .min(4, {
      message: 'Nome precisa ter no mínimo 4 caracteres.',
    }),
  created_at: z.coerce.date().optional(),
  updated_at: z.coerce.date().optional(),
});

type RuasFormValues = z.infer<typeof ruasFormSchema>;

export function RuasForm({ initialData }: { initialData: any }) {
  const router = useRouter();
  const form = useForm<RuasFormValues>({
    resolver: zodResolver(ruasFormSchema),
    defaultValues: initialData ? initialData : { nome: '' },
    mode: 'onChange',
  });

  async function onSubmit(data: RuasFormValues) {
    if (initialData) {
      await updateRua(data)
        .then((res) => {
          toast({
            title: 'Ruas do Estoque',
            description: (
              <>
                <p>Rua {data.nome} foi salvo com sucesso!</p>
              </>
            ),
          });
          router.push('/configuracoes/ruas');
        })
        .catch((error) => {
          toast({
            duration: 2000,
            variant: 'destructive',
            title: 'Erro: Ruas do Estoque',
            description: (
              <>
                <p>Erro ao gravar.</p>
                <p>{error.message}</p>
              </>
            ),
          });
        })
        .finally(() => {});
    } else {
      await insertRua(data)
        .then((res) => {
          toast({
            title: 'Ruas do Estoque',
            description: (
              <>
                <p>Rua {data.nome} foi salvo com sucesso!</p>
              </>
            ),
          });
          router.push('/configuracoes/ruas');
        })
        .catch((error) => {
          toast({
            duration: 2000,
            variant: 'destructive',
            title: 'Erro: Ruas do Estoque',
            description: (
              <>
                <p>Erro ao gravar.</p>
                <p>{error.message}</p>
              </>
            ),
          });
        })
        .finally(() => {});
    }
  }

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>
          <FormField
            control={form.control}
            name='nome'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nome</FormLabel>
                <FormControl>
                  <Input
                    placeholder='Informe o Nome'
                    className='resize-none'
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {initialData && (
            <>
              <FormField
                control={form.control}
                name='created_at'
                render={({ field }) => (
                  <FormItem className='flex flex-col'>
                    <FormLabel>Data de Criação</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={'outline'}
                            className={cn(
                              'w-[240px] pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'dd/MM/yyyy HH:mm')
                            ) : (
                              <span>Selecione uma Data</span>
                            )}
                            <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className='w-auto p-0' align='start'>
                        <Calendar
                          mode='single'
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date('1900-01-01')
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='updated_at'
                render={({ field }) => (
                  <FormItem className='flex flex-col'>
                    <FormLabel>Data de Alteração</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={'outline'}
                            className={cn(
                              'w-[240px] pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'dd/MM/yyyy HH:mm')
                            ) : (
                              <span>Selecione uma Data</span>
                            )}
                            <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className='w-auto p-0' align='start'>
                        <Calendar
                          mode='single'
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date('1900-01-01')
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          )}

          <Button type='submit' variant='outline'>
            Salvar
          </Button>
        </form>
      </Form>
    </>
  );
}
