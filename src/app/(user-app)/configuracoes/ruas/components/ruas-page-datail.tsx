'use client';

import { useEffect, useMemo, useState } from 'react';
import { columns } from './columns';
import { DataTable } from './data-table';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/hooks/use-toast';
import { getRuas } from '@/service/alocacoes-service';
import { Rua } from '@/data/model/Alocacao';

export default function RuasPageDetail() {
  const [registros, setRegistros] = useState<Rua[]>([]);
  const [isLoading, setIsLoading] = useState<Boolean>(true);

  function getRuasList() {
    getRuas()
      .then((res) => {
        setRegistros(res ? res : []);
      })
      .catch((error) => {
        toast({
          duration: 2000,
          variant: 'destructive',
          title: 'Erro: Ruas de Estoque',
          description: (
            <>
              <p>Erro ao carregar Ruas.</p>
              <p>{error.message}</p>
            </>
          ),
        });
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  const onRefresh = () => {
    getRuasList();
  };

  useEffect(() => {
    getRuasList();
  }, []);

  return (
    <>
      {isLoading ? (
        <Skeleton className='h-2/3 rounded-sm' />
      ) : (
        <DataTable
          showToolbar={true}
          data={registros}
          columns={columns({ onRefresh })}
        />
      )}
    </>
  );
}
