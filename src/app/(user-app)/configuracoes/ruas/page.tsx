import { Metadata } from 'next';
import { But<PERSON> } from '@/components/ui/button';
import ButtonLink from '@/components/app-button-link';
import RuasPageDetail from './components/ruas-page-datail';

export const metadata: Metadata = {
  title: 'Ruas do Estoque',
  description: 'Listagem de Ruas do Estoque',
};

export default async function RuasPage() {
  return (
    <>
      <div className='hidden h-full flex-1 flex-col space-y-8 p-8 md:flex'>
        <div className='flex items-center justify-between space-y-2'>
          <div className='flex justify-between w-full'>
            <h2 className='text-2xl font-bold tracking-tight'>
              Ruas do Estoque
            </h2>
            <ButtonLink url='ruas/new'>
              {' '}
              <Button variant='outline'>Novo</Button>
            </ButtonLink>
          </div>
        </div>
        <RuasPageDetail />
      </div>
    </>
  );
}
