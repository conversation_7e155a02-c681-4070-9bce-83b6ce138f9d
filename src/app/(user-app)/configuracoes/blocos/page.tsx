import { Metada<PERSON> } from 'next';
import { But<PERSON> } from '@/components/ui/button';
import ButtonLink from '@/components/app-button-link';
import BlocosPageDetail from './components/blocos-page-datail';

export const metadata: Metadata = {
  title: 'Blocos do Estoque',
  description: 'Listagem de Blocos do Estoque',
};

export default async function BlocosPage() {
  return (
    <>
      <div className='hidden h-full flex-1 flex-col space-y-8 p-8 md:flex'>
        <div className='flex items-center justify-between space-y-2'>
          <div className='flex justify-between w-full'>
            <h2 className='text-2xl font-bold tracking-tight'>
              Blocos do Estoque
            </h2>
            <ButtonLink url='blocos/new'>
              {' '}
              <Button variant='outline'>Novo</Button>
            </ButtonLink>
          </div>
        </div>
        <BlocosPageDetail />
      </div>
    </>
  );
}
