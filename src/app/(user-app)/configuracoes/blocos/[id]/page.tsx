import { toast } from '@/hooks/use-toast';
import { getBlocosById } from '@/service/alocacoes-service';
import { BlocosForm } from '../components/blocos-form';

export default async function BlocosEditPage({ params }: { params: any }) {
  const { id } = params;
  let bloco: any;

  if (id !== 'new') {
    await getBlocosById(id)
      .then((res) => {
        bloco = res;
      })
      .catch((error) => {
        toast({
          duration: 2000,
          variant: 'destructive',
          title: 'Erro: Blocos do Estoque',
          description: (
            <>
              <p>Erro ao carregar Blocos do Estoque.</p>
              <p>{error.message}</p>
            </>
          ),
        });
      });
  }

  return (
    <div className='container mx-auto py-4'>
      <h1 className='text-2xl font-bold mb-4'>
        {id === 'new'
          ? 'Nova Bloco do Estoque'
          : `Alterar Bloco do Estoque: ${bloco ? bloco!.nome : ''}`}
      </h1>
      <BlocosForm initialData={bloco} />
    </div>
  );
}
