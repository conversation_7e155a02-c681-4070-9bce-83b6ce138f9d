'use client';

import { useEffect, useMemo, useState } from 'react';
import { columns } from './columns';
import { DataTable } from './data-table';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/hooks/use-toast';
import { getBlocos } from '@/service/alocacoes-service';
import { Bloco } from '@/data/model/Alocacao';

export default function BlocosPageDetail() {
  const [registros, setRegistros] = useState<Bloco[]>([]);
  const [isLoading, setIsLoading] = useState<Boolean>(true);

  function getBlocosList() {
    getBlocos()
      .then((res) => {
        setRegistros(res ? res : []);
      })
      .catch((error) => {
        toast({
          duration: 2000,
          variant: 'destructive',
          title: 'Erro: Blocos de Estoque',
          description: (
            <>
              <p>Erro ao carregar Blocos.</p>
              <p>{error.message}</p>
            </>
          ),
        });
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  const onRefresh = () => {
    getBlocosList();
  };

  useEffect(() => {
    getBlocosList();
  }, []);

  return (
    <>
      {isLoading ? (
        <Skeleton className='h-2/3 rounded-sm' />
      ) : (
        <DataTable
          showToolbar={true}
          data={registros}
          columns={columns({ onRefresh })}
        />
      )}
    </>
  );
}
