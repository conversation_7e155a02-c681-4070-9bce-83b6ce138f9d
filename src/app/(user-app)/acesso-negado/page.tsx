"use client";

import { But<PERSON> } from "@/components/ui/button";

export default function AcessoNegadoPage() {
  return (
    <div style={{ minHeight: "100vh", display: "flex", flexDirection: "column", alignItems: "center", justifyContent: "center" }}>
      <h1 style={{ fontSize: "2rem", fontWeight: "bold", color: "#dc2626" }}>Acesso negado</h1>
      <p style={{ marginTop: "1rem", color: "#555" }}>
        Você não tem permissão para acessar esta página.
      </p>
      <Button
        style={{ marginTop: "2rem" }}
        onClick={() => window.history.back()}
        variant="outline"
      >
        Voltar
      </Button>
    </div>
  );
}