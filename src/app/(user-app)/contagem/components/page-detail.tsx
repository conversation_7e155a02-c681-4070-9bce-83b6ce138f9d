'use client';

import React, { useEffect, useState } from 'react'
import { createClient } from '../../../../../utils/supabase/client';
import { Skeleton } from '@/components/ui/skeleton';
import { DataTable } from './data-table';
import { columns } from './columns';

export default function ContagemPageDetail() {
  const supabase = createClient()
  const [registros, setRegistros] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<Boolean>(true);

  useEffect(() => {
    const getContagem = async () => {
      const { data, error } = await supabase
        .from('contagens')
        .select(
          `id, created_at, numero_controle, user_profiles (email), ordens_producao (op)`
        )
        .eq('ativo', true);

      if (!error && data) {        
        const formattedData = data.map((item: any) => ({
          ...item,
          email: item.user_profiles.email,
          op: item.ordens_producao.op,
        }));

        // Remover o campo `user_profiles` agora desnecessário
        formattedData.forEach((item) => delete item.user_profiles);
        formattedData.forEach((item) => delete item.ordens_producao);

        setRegistros(formattedData);
        setIsLoading(false);
      }
    };

    getContagem().catch(() => {
      setIsLoading(false);
      console.error('Erro ao obter dados do Supabase.');
    });
  }, []);
  return (
    <>
      {isLoading ? (
        <Skeleton className='h-2/3 rounded-sm' />
      ) : (
        <DataTable showToolbar={true} data={registros} columns={columns} />
      )}
    </>
  )
}
