"use client";

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  FilterFn,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Lote } from "@/data/model/Lote";
import { toast } from "@/hooks/use-toast";
import { DataTablePagination } from "./data-table-pagination";
import { useRouter } from "next/navigation";
import { DataTableToolbarOP } from "./data-table-toolbar";
import { createClient } from "../../../../../utils/supabase/client";
import { OrdemProducao } from "@/data/model/OrdemProducao";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
}

export function ContagemFormNew<TData, TValue>({
  columns,
  data,
}: DataTableProps<TData, TValue>) {
  const supabase = createClient();
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] = React.useState<
    VisibilityState
  >({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [globalFilter, setGlobalFilter] = React.useState("");
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [saveDisabled, setSaveDisabled] = React.useState(false);
  const router = useRouter();

  const globalFilterFn: FilterFn<TData> = (row, columnId, value) => {
    const itemValue = row.getValue(columnId);
    return itemValue !== null && itemValue !== undefined
      ? String(itemValue)
        .toLowerCase()
        .includes(String(value).toLowerCase())
      : false;
  };

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      globalFilter,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    globalFilterFn,
  });

  async function inserContagem(opId: string): Promise<string | null> {
    try {
      const newContagem = { "ordem_producao_id": opId };

      const { data, error } = await supabase
        .from("contagens")
        .insert(newContagem)
        .select();

      if (!error && data) {
        setSaveDisabled(false);

        toast({
          duration: 3000,
          title: "Contagem",
          description: <p>Contagem gravada com sucesso!</p>,
        });

        router.push("/contagem");
      } else {
        setSaveDisabled(false);
        var errorMessage = ""

        if (error.code === '23505') {
          errorMessage = "Já existe uma contagem para esta OP!"
        } else {
          errorMessage = "Erro ao gravar contagem: " + error.message
        }

        toast({
          variant: 'destructive',
          duration: 3000,
          title: 'Seleção',
          description: <p>{errorMessage}</p>,
        });
      }
    } catch (error) {
      setSaveDisabled(false);

      console.error(
        "Erro ao inserir Contagem no Supabase:",
        error,
      );
    }

    return null;
  }

  async function handleSave() {
    if (table.getSelectedRowModel().rows.length <= 0) {
      toast({
        variant: "destructive",
        duration: 2000,
        title: "Alerta: Contagem",
        description: <p>Você precisa selecionar uma OP!</p>,
      });
      return;
    }

    setSaveDisabled(true);

    const selectedOP = table.getSelectedRowModel().rows.map((row) => {
      const op = row.original as OrdemProducao;
      return op;
    });

    await inserContagem(selectedOP[0].id);
  }

  return (
    <div className="space-y-4">
      <DataTableToolbarOP table={table} />
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} colSpan={header.colSpan}>
                      {header.isPlaceholder ? null : flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length
              ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              )
              : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    Nenhum resultado.
                  </TableCell>
                </TableRow>
              )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination table={table} />

      <Button onClick={handleSave} variant="outline" disabled={saveDisabled}>
        Salvar
      </Button>
    </div>
  );
}
