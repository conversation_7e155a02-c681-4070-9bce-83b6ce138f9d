"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { CalendarIcon } from "@radix-ui/react-icons";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { Contagem } from "@/data/model/Contagem";
import { DataTable } from "./data-table";
import { columnsContagemItem } from "./columns-contagem-item";

const contagemFormSchema = z.object({
  id: z.string().optional(),
  created_at: z.date().optional(),
  email: z.string().optional(),
  numero_controle: z.string().optional(),
  op: z.string().optional(),
});

type ContagemFormValues = z.infer<typeof contagemFormSchema>;

export function ContagemFormShow({
  initialData,
  parcelasContadas,
}: {
  initialData: any;
  parcelasContadas: any[] | null;
}) {
  const form = useForm<ContagemFormValues>({
    resolver: zodResolver(contagemFormSchema),
    defaultValues: initialData,
    mode: "onChange",
  });

  async function onSubmit(data: any) {}

  return (
    <>
      <div className="mt-6">
        <DataTable
          showToolbar={true}
          columns={columnsContagemItem}
          data={parcelasContadas ? parcelasContadas : []}
        />
      </div>
    </>
  );
}
