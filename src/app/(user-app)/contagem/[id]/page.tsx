import { createClient } from '../../../../../utils/supabase/server';
import { ContagemFormShow } from '../components/contagem-form-show';
import ContagemNew from '../components/contagem-new';

async function getNumeroContagem(contagem_id: string): Promise<any[]> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('contagens')
      .select('*')
      .eq('id', contagem_id)    

    if (!error && data) {
      
      return data
    }

  } catch (error) {
    console.error('Erro ao buscar dados para do Supabase:', error);
  }

  return [];
}

async function getParcelasContadas(contagem_id: string): Promise<any[]> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('vw_parcelas_contadas')
      .select('*')
      .eq('contagem_id', contagem_id)    

    if (!error && data) {
      //console.log(JSON.stringify(data, null, 2))

      const formattedData = data.map((item) => ({
        ...item,
        data_hora_contagem: item.data_hora_contagem ? new Date(item.data_hora_contagem) : null, // Converte ou mantém null
      }));

      return formattedData;
    }

  } catch (error) {
    console.error('Erro ao buscar dados para do Supabase:', error);
  }

  return [];
}

export default async function ContagemEditPage({
  params,
}: {
  params: any;
}) {
  const { id } = params;
  const parcelasContadas = await getParcelasContadas(id)
  const contagem = await getNumeroContagem(id)
  const numero_contagem = contagem?.[0]?.numero_controle ?? "Indefinido";

  return (
    <div className='container mx-auto py-4'>
      <h1 className='text-2xl font-bold mb-4'>
        {id === 'new'
          ? 'Nova Contagem'
          : `Contagem: ${numero_contagem}`}
      </h1>
      {
        id === 'new' ? (
        <ContagemNew />
      ) : (
        <ContagemFormShow
          parcelasContadas={parcelasContadas}
          initialData={contagem}
        />
      )}
    </div>
  );
}
