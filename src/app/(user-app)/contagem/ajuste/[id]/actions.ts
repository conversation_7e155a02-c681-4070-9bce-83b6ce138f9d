'use server'

import { ParcelaContada } from '@/data/model/ParcelaContada';
//import { revalidatePath } from 'next/cache'
import { createClient } from '../../../../../../utils/supabase/server'

export async function getParcelaByNcc(ncc: string): Promise<any | undefined> {
  const supabase = createClient();

  const { data, error } = await supabase.
    from('parcelas')
    .select('*')
    .eq('ncc', ncc);

  if (!error && data) {
    return data[0];
  } else {
    throw new Error(error.message);
  }

  //revalidatePath('/', 'layout')
}

export async function getParcelaContadaByParcelaOPId(parcelaOPId: string): Promise<any | undefined> {
  const supabase = createClient();

  const { data, error } = await supabase.
    from('envelopes_contagem')
    .select('*')
    .eq('parcela_ordem_producao_id', parcelaOPId);

  if (!error && data) {
    return data[0];
  } else {
    throw new Error(error.message);
  }

  //revalidatePath('/', 'layout')
}

export async function insertParcelaContada(parcela_contada: any): Promise<ParcelaContada | undefined> {
  const supabase = createClient();

  const { data, error } = await supabase.from('parcelas_contadas').insert(parcela_contada).select();
  if (!error && data) {
    return data[0];
  } else {
    throw new Error(error.message);
  }
}

export async function updateParcelaContada(id: string, parcela_contada: any): Promise<ParcelaContada | undefined> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('envelopes_contagem')
    .update(parcela_contada)
    .eq('id', id)
    .select();
  if (!error && data) {
    return data[0];
  } else {
    throw new Error(error.message);
  }
}
