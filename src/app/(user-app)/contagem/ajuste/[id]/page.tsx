//import { toast } from '@/hooks/use-toast';
import { toast } from '@/hooks/use-toast';
import { AjustesForm } from '../components/ajustes-form';
import { getParcelaContadaByParcelaOPId } from './actions';

export default async function AjustesEditPage({ params }: { params: any }) {
  const { id } = params;
  const parametros = id.split('_')
  const [ncc, contagemId, parcelaOrdemProducaoId] = parametros

  let parcelaContada: any;

  if (id !== 'new') {

    if (parcelaOrdemProducaoId) {
      await getParcelaContadaByParcelaOPId(parcelaOrdemProducaoId)
      .then((res) => {
        parcelaContada = res;
      })
      .catch((error) => {
        toast({
          duration: 2000,
          variant: 'destructive',
          title: 'Erro: Envelopes da Contagem',
          description: (
            <>
              <p>Erro ao carregar Ruas do Estoque.</p>
              <p>{error.message}</p>
            </>
          ),
        });
      });      
    }
  }

  return (
    <div className='container mx-auto py-4'>
      <h1 className='text-2xl font-bold mb-4'>
        {id === 'new'
          ? 'Novo Ajuste da Contagem'
          : `Ajustar Contagem do NCC: ${ncc ? ncc : ''}`}
      </h1>
      <AjustesForm initialData={parcelaContada ? parcelaContada : parametros} />
    </div>
  );
}
