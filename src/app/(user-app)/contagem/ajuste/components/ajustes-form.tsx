'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useEffect, useState } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { useRouter } from 'next/navigation';
import { updateParcelaContada } from '../[id]/actions';
import { toast } from '@/hooks/use-toast';
import { MotivoAjusteContagem, getMotivosAjusteContagem } from '@/service/motivos-ajuste-contagem-service';

const ajustesFormSchema = z.object({
  id: z.string().optional(),
  envelope: z
    .string({
      required_error: 'Informe o número do envelope.',
    })
    .min(1, { message: 'Envelope não pode estar vazio.' }),
  motivo_ajuste_contagem_id: z.string({
    required_error: 'Selecione o motivo do ajuste.',
  }).min(1, { message: 'Selecione o motivo do ajuste.' }),
  observacoes: z.coerce.string().optional(),
});

type AjustesFormValues = z.infer<typeof ajustesFormSchema>;

export function AjustesForm({ initialData }: { initialData: any }) {
  const [motivosAjuste, setMotivosAjuste] = useState<MotivoAjusteContagem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    async function loadMotivosAjuste() {
      try {
        setIsLoading(true);
        const motivos = await getMotivosAjusteContagem();
        console.log('Motivos carregados:', motivos);
        
        if (motivos) {
          setMotivosAjuste(motivos);
        }
      } catch (error: any) {
        console.error('Erro ao carregar motivos:', error);
        toast({
          variant: 'destructive',
          title: 'Erro ao carregar motivos de ajuste',
          description: error.message,
        });
      } finally {
        setIsLoading(false);
      }
    }

    loadMotivosAjuste();
  }, []);

  let newObject = { 
    envelope: "", 
    observacoes: "",
    motivo_ajuste_contagem_id: ""
  }

  if (initialData.hasOwnProperty('numero_envelope')) {
    newObject.envelope = initialData.numero_envelope;
    newObject.observacoes = initialData.observacoes ?? "";
    newObject.motivo_ajuste_contagem_id = initialData.motivo_ajuste_contagem_id || "";
  }

  const form = useForm<AjustesFormValues>({
    resolver: zodResolver(ajustesFormSchema),
    defaultValues: newObject,
    mode: 'onChange',
  });

  async function onSubmit(data: AjustesFormValues) {
    if (initialData && initialData.hasOwnProperty('numero_envelope')) {
      // Find the "Outros" motivo id from motivosAjuste
      const motivoOutros = motivosAjuste.find(m => m.nome === "Outros");
      const idOutros = motivoOutros ? motivoOutros.id : null;
  
      // If motivo is "Outros", observacoes must be non-empty
      if (data.motivo_ajuste_contagem_id === idOutros && (!data.observacoes || data.observacoes.trim() === "")) {
        // Set form error for observacoes
        form.setError("observacoes", {
          type: "manual",
          message: "Observações é obrigatório quando o motivo é 'Outros'.",
        });
        return; // Prevent submission
      }
  
      const updateData = {
        observacoes: data.observacoes,
        motivo_ajuste_contagem_id: data.motivo_ajuste_contagem_id,
        concluido: true
      }
  
      try {
        await updateParcelaContada(initialData.id, updateData);
        toast({
          title: 'Ajustes da Contagem',
          description: <p>O ajuste foi atualizado com sucesso!</p>,
        });
        router.push('/contagem');
      } catch (error: any) {
        toast({
          duration: 2000,
          variant: 'destructive',
          title: 'Erro: Ajustes da Contagem',
          description: (
            <>
              <p>Erro ao gravar.</p>
              <p>{error.message}</p>
            </>
          ),
        });
      }
    }
  }

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>
          <FormField
            control={form.control}
            name='motivo_ajuste_contagem_id'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Motivo do Ajuste</FormLabel>
                {isLoading ? (
                  <div>Carregando motivos...</div>
                ) : (
                  <Select 
                    onValueChange={field.onChange} 
                    value={field.value} // Alterado de defaultValue para value
                    required
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o motivo do ajuste" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {motivosAjuste && motivosAjuste.length > 0 ? (
                        motivosAjuste.map((motivo) => (
                          <SelectItem key={motivo.id} value={motivo.id}>
                            {motivo.nome}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-options" disabled>
                          Nenhum motivo disponível
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                )}
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='observacoes'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Observações</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder='Informe observações adicionais'
                    className='resize-none h-64'
                    {...field}
                    value={field.value ?? ""}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button type='submit' variant='outline'>
            Salvar
          </Button>
        </form>
      </Form>
    </>
  );
}
