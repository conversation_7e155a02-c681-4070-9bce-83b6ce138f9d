"use client";

import { Cross2Icon } from "@radix-ui/react-icons";
import { Table } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState, useEffect } from "react";
import { RefreshCwIcon } from "lucide-react";
import { DataTableViewOptions } from "./data-table-view-options-ref";

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  onSearchChange: (value: string) => void;
  search: string;
  onRefresh?: () => void;
}

export function DataTableToolbar<TData>({
  table,
  onSearchChange,
  search,
  onRefresh,
}: DataTableToolbarProps<TData>) {
  const [inputValue, setInputValue] = useState(search);

  useEffect(() => {
    setInputValue(search);
  }, [search]);

  useEffect(() => {
    const handler = setTimeout(() => {
      onSearchChange(inputValue);
    }, 500);

    return () => {
      clearTimeout(handler);
    };
  }, [inputValue, onSearchChange]);

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-2">
        <Input
          placeholder="Filtrar..."
          value={inputValue}
          onChange={(event) => setInputValue(event.target.value)}
          className="h-8 w-[150px] lg:w-[250px]"
        />
        {inputValue && (
          <Button
            variant="ghost"
            onClick={() => {
              setInputValue("");
              onSearchChange("");
            }}
            className="h-8 px-2 lg:px-3"
          >
            Limpar filtros
            <Cross2Icon className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex items-center space-x-2">
        {onRefresh && (
          <Button
            variant="outline"
            onClick={onRefresh}
            className="h-8 px-2 lg:px-3"
            size="sm"
          >
            <RefreshCwIcon className="mr-2 h-4 w-4" />
            Recarregar
          </Button>
        )}
        <DataTableViewOptions table={table} />
      </div>
    </div>
  );
}
