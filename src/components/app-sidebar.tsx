"use client";

import * as React from "react";
import {
  BarChart2,
  BookOpen,
  Bot,
  Calculator,
  Command,
  Factory,
  FileInput,
  Filter,
  Frame,
  Home,
  LifeBuoy,
  Map,
  PackageCheck,
  PieChart,
  Send,
  Settings2,
  SortAsc,
  SquareTerminal,
  Truck,
  Users,
  Warehouse,
} from "lucide-react";

import { NavMain } from "@/components/nav-main";
import { NavSecondary } from "@/components/nav-secondary";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { SearchForm } from "./search-form";
import AppNavUser from "./app-navuser";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { usePathname } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";

interface AppSidebarProps {
  collapsible?: "none" | "icon" | "offcanvas";
}

function getConfigItems(isAdmin: boolean) {
  const items = [];

  if (isAdmin) {
    items.push({
      title: "Usuários",
      url: "/usuarios",
    });
  }

  items.push(
    {
      title: "Ruas",
      url: "/configuracoes/ruas",
    },
    {
      title: "Blocos",
      url: "/configuracoes/blocos",
    },
    {
      title: "Baias",
      url: "/configuracoes/baias",
    }
  );

  return items;
}

export function AppSidebar({ collapsible }: AppSidebarProps) {
  const pathname = usePathname();
  const { isAdmin } = useAuth();

  const dataSideBar = {
    user: {
      name: "shadcn",
      email: "<EMAIL>",
      avatar: "/avatars/shadcn.jpg",
    },
    navMain: [
      {
        title: "Principal",
        url: "#",
        icon: Home,
        isActive: true,
        items: [
          {
            title: "Home-Dashboard",
            url: "/home",
          },
        ],
      },
      {
        title: "Importações",
        url: "#",
        icon: FileInput,
        items: [
          {
            title: "Documentos",
            url: "/file-upload",
          },
          {
            title: "Dashboard",
            url: "/file-upload/documentos-dashboard",
          },
        ],
      },
      {
        title: "Beneficiamento",
        url: "#",
        icon: Factory,
        items: [
          {
            title: "Beneficiamentos",
            url: "/beneficiamento/harvest-files-uploaded",
          },
          {
            title: "Pesos de Beneficiamentos",
            url: "/beneficiamento/processing-files-uploaded",
          },
          {
            title: "Dashboard",
            url: "/beneficiamento/beneficiamento-dashboard",
          },
        ],
      },
      {
        title: "Estoque",
        url: "#",
        icon: Warehouse,
        items: [
          {
            title: "Entrada",
            url: "/estoque/entrada-estoque",
          },
          {
            title: "Saída",
            url: "/estoque/saida-estoque",
          },
          {
            title: "Alocações",
            url: "/estoque/alocacoes",
          },
          {
            title: "Desalocações",
            url: "/estoque/desalocacoes",
          },
          {
            title: "Dashboard",
            url: "/estoque/rastreio-dashboard",
          },
          {
            title: "Planta Baixa",
            url: "/estoque/planta",
          },
        ],
      },
      {
        title: "Seleção",
        url: "",
        icon: Filter,
        items: [
          {
            title: "Seleções",
            url: "/selecao",
          },
          {
            title: "Dashboard",
            url: "/selecao/selecao-dashboard",
          },
        ],
      },
      {
        title: "Estratificação",
        url: "",
        icon: BarChart2,
        items: [
          {
            title: "Estratificações",
            url: "/estratificacao",
          },
          {
            title: "Dashboard",
            url: "/estratificacao/estratificacao-dashboard",
          },
        ],
      },
      {
        title: "Contagem",
        url: "",
        icon: Calculator,
        items: [
          {
            title: "Contagens",
            url: "/contagem",
          },
          {
            title: "Dashboard",
            url: "/contagem/contagem-dashboard",
          },
        ],
      },
      {
        title: "Ordenagem",
        url: "",
        icon: SortAsc,
        items: [
          {
            title: "Ordenagens",
            url: "/ordenagem",
          },
          {
            title: "Dashboard",
            url: "/ordenagem/ordenagem-dashboard",
          },
        ],
      },
      {
        title: "Expedição",
        url: "",
        icon: Truck,
        items: [
          {
            title: "Expedições",
            url: "/expedicao",
          },
          {
            title: "Dashboard",
            url: "/expedicao/expedicao-dashboard",
          },
        ],
      },
      {
        title: "BI",
        url: "",
        icon: PieChart,
        items: [
          {
            title: "Dashboards",
            url: "/bi/dashboards",
          },
        ],
      },
      {
        title: "Configurações",
        url: "#",
        icon: Settings2,
        items: getConfigItems(isAdmin),
      },
    ],
  };

  return (
    <Sidebar variant="inset" collapsible={collapsible}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <Link href="/">
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                  <Command className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">TMG Agro</span>
                  <span className="truncate text-xs">Hadar</span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={dataSideBar.navMain} />
      </SidebarContent>
      <SidebarFooter>
        <AppNavUser />
      </SidebarFooter>
    </Sidebar>
  );
}
