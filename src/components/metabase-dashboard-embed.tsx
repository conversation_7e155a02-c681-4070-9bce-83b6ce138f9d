'use client'

// components/MetabaseDashboardEmbed.tsx
import React, { useEffect, useState } from "react";

interface MetabaseDashboardEmbedProps {
  dashboardId: number;
}

const MetabaseDashboardEmbed: React.FC<MetabaseDashboardEmbedProps> = ({ dashboardId }) => {
  const [iframeUrl, setIframeUrl] = useState<string>("");

  useEffect(() => {
    const fetchIframeUrl = async () => {
      try {
        const response = await fetch(`/api/generateMetabaseToken?dashboardId=${dashboardId}`);
        const data = await response.json();
        if (data.iframeUrl) {
          setIframeUrl(data.iframeUrl);
        } else {
          console.error("Erro ao obter o iframe URL:", data.error);
        }
      } catch (error) {
        console.error("Erro ao buscar o iframe URL:", error);
      }
    };

    fetchIframeUrl();
  }, [dashboardId]);

  return (
    <div className="flex flex-col items-center justify-center w-full h-full">
      {iframeUrl ? (
        <iframe
          src={iframeUrl}
          className="w-full h-[90vh] border border-gray-300 rounded-lg shadow-md"
          allow="fullscreen"
          title="Metabase Dashboard"
        />
      ) : (
        <p className="text-gray-500 text-lg">Carregando o dashboard...</p>
      )}
    </div>
  );
};

export default MetabaseDashboardEmbed;