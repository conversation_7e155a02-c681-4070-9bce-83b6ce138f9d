"use client";

import {
  <PERSON><PERSON>crumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

import { usePathname } from "next/navigation";

const routeTranslations: Record<string, string> = {
  settings: "Configurações",
  "harvest-files-uploaded": "Upload de Arquivos de Colheita",
  "processing-files-uploaded": "Upload de Arquivos de Beneficiamento",
  "file-upload": "Upload de Arquivos",
  "entrada-estoque": "Entrada de Estoque",
  "saida-estoque": "Saída de Estoque",
  "alocacoes": "Alocações de Estoque",
  "desalocacoes": "Desalocações de Estoque",
  "rastreio-dashboard": "Dashboard de Rastreio",
  "selecao": "Seleção",
  "estratificacao": "Estratificação",
  "harvest-order": "Ordens de Colheita",
  "processing-order": "Ordens de Beneficiamento",
  "expedicao": "Expedições",
  "bi": "BI",
  "dashboards": "Dashboards",
  // Adicione mais traduções conforme necessário
};

export default function AppBreadCrumb() {
  const paths = usePathname();
  const pathNames = paths.split("/").filter((path) => path);

  function getTranslatedPath(path: string) {
    return routeTranslations[path.toLowerCase()] ||
      path[0].toUpperCase() + path.slice(1);
  }

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {pathNames.map((link, index) => {
          let href = `/${pathNames.slice(0, index + 1).join("/")}`;
          let itemLink = getTranslatedPath(link);

          return (
            <span className="flex gap-2.5 items-center" key={index}>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href={href}>{itemLink}</BreadcrumbLink>
              </BreadcrumbItem>

              {pathNames.length !== index + 1 && (
                <BreadcrumbSeparator className="hidden md:block" />
              )}
            </span>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
