import * as React from 'react';
import { useEffect, useState } from 'react';
import { createClient } from '../../utils/supabase/client';
import { NavUser } from '@/components/nav-user';
import { Icons } from './ui/icons';

interface User {
  name: string;
  email: string;
  avatar: string;
}

export default function AppNavUser() {
  let [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = React.useState<boolean>(true);

  useEffect(() => {
    const supabase = createClient();
    const getUserLoggedIn = async () => {
      const { data, error } = await supabase.auth.getSession();

      if (data.session?.user) {
        const myUser = {
          name: '',
          email: data.session?.user.email,
          avatar: '',
        };
        setUser(myUser as User);
        setIsLoading(false);
      }
    };
    getUserLoggedIn().catch((error) => {
      setIsLoading(false);
    });
  }, []);

  return (
    <>
      {isLoading && (
        <span className='flex items-center justify-center'>
          <Icons.spinner className='mr-2 h-4 w-4 animate-spin' />
        </span>
      )}
      {user && <NavUser user={user} />}
    </>
  );
}
