"use client";

import React, { useCallback, useEffect, useRef, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Download, File as FileIcon, Upload, X } from "lucide-react";
import * as XLSX from "xlsx";
import { createClient } from "../../utils/supabase/server";
import { HarvestOrder } from "@/data/model/HarvestOrder";
import {
  persistCaixasExpedicaoToSupabase,
  persistEnvelopeContagemToSupabase,
  persistHarvestOrderToSupabase,
  persistProcessingOrderToSupabase,
  persistProductionOrderToSupabase,
  persistWeightUpdateToSupabase,
} from "@/service/files-service";
import { array } from "zod";
import { count } from "console";
import { ProcessingOrder } from "@/data/model/ProcessingOrder";
import { TipoArquivo } from "@/data/model/TipoArquivo";
import { ProductionOrder } from "@/data/model/ProductionOrder";
import {
  DocumentoEnvelopeContagem,
  EnvelopeContagem,
} from "@/data/model/EnvelopeContagem";
import { DocumentoExpedicao } from "@/data/model/CaixaExpedicao";
//import Papa from 'papaparse'

const ALLOWED_FILE_TYPES = [
  "application/vnd.ms-excel",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  "text/csv",
];

const FILE_TYPE_EXTENSIONS = {
  "application/vnd.ms-excel": "xls",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "xlsx",
  "text/csv": "csv",
};

export default function FileUpload() {
  const [files, setFiles] = useState<File[]>([]);
  const [message, setMessage] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [harvestOrder, setHarvestOrder] = useState<HarvestOrder[]>([]);
  const [processingOrder, setProcessingOrder] = useState<ProcessingOrder[]>([]);
  const [productionOrder, setProductionOrder] = useState<ProductionOrder[]>([]);
  const [weightUpdateOrder, setWeightUpdateOrder] = useState<ProcessingOrder[]>(
    []
  );
  const [documentoEnvelopeContagem, setDocumentoEnvelopeContagem] = useState<
    DocumentoEnvelopeContagem[]
  >([]);
  const [documentoExpedicao, setDocumentoExpedicao] = useState<
    DocumentoExpedicao[]
  >([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const dropZoneRef = useRef<HTMLDivElement>(null);
  const [data, setData] = useState("");
  const tipoArquivoRef = useRef<TipoArquivo | null>(null);

  const validateFile = (file: File): boolean => {
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      setMessage(
        `Tipo de arquivo não permitido. Por favor, selecione arquivos XLS, XLSX ou CSV.`
      );
      return false;
    }
    return true;
  };

  const handleFiles = useCallback((newFiles: FileList | null) => {
    if (newFiles) {
      const validFiles = Array.from(newFiles).filter(validateFile);
      setFiles((prevFiles) => [...prevFiles, ...validFiles]);
      if (validFiles.length > 0) {
        setMessage(null);
        setHarvestOrder([]); // Limpa os dados extraídos quando novos arquivos são adicionados
        setProcessingOrder([]);
      }
    }
  }, []);

  const extractHarvestOrderData = async (
    files: File[]
  ): Promise<HarvestOrder[]> => {
    const harvestOrderData: HarvestOrder[] = [];

    for (const file of files) {
      const arrayBuffer = await file.arrayBuffer();
      const workbook = XLSX.read(arrayBuffer);
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];

      const allRows = XLSX.utils.sheet_to_json(sheet, { header: 1 });
      const headers = allRows[0] as string[];

      // Encontrar índices das colunas necessárias
      const linhagemIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("linhagem")
      );
      const nccIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("ncc")
      );
      const loteColheitaIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("lote")
      );
      const tagIdIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("tag_id")
      );

      // Validar se todas as colunas necessárias foram encontradas
      if (
        [linhagemIndex, nccIndex, loteColheitaIndex, tagIdIndex].includes(-1)
      ) {
        setMessage(
          "Arquivo inválido: Algumas colunas necessárias não foram encontradas"
        );
        return [];
      }

      const data = allRows.slice(1).map((row: any) => ({
        nc: String(null),
        linhagem: String(row[linhagemIndex]),
        experimento: String(null),
        ncc: String(row[nccIndex]),
        tag_id: row[tagIdIndex],
        lote_colheita: String(row[loteColheitaIndex]),
      }));

      const listaFiltrada = data.filter((item) => item.tag_id !== undefined);
      harvestOrderData.push(...listaFiltrada);
    }

    tipoArquivoRef.current = TipoArquivo.OrdemColheita;
    return harvestOrderData;
  };

  const extractProcessingOrderData = async (
    files: File[]
  ): Promise<ProcessingOrder[]> => {
    const processingOrderData: ProcessingOrder[] = [];

    for (const file of files) {
      const arrayBuffer = await file.arrayBuffer();
      const workbook = XLSX.read(arrayBuffer);
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];

      const allRows = XLSX.utils.sheet_to_json(sheet, { header: 1 });
      const headers = allRows[0] as string[];

      // Encontrar índices das colunas necessárias
      const culturaIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("cultura")
      );
      const loteIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("lote")
      );
      const nccIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("ncc")
      );
      const linhagemIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("genotipo")
      );
      const pesoIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("peso")
      );

      // Validar se todas as colunas necessárias foram encontradas
      if (
        [culturaIndex, loteIndex, nccIndex, linhagemIndex, pesoIndex].includes(
          -1
        )
      ) {
        //throw new Error('Arquivo inválido: Algumas colunas necessárias não foram encontradas')
        setMessage(
          "Arquivo inválido: Algumas colunas necessárias não foram encontradas"
        );
        return [];
      }

      const data = allRows.slice(1).map((row: any) => ({
        nc: String(null),
        linhagem: String(row[linhagemIndex]),
        ncc: String(row[nccIndex]),
        lote: String(row[loteIndex]),
        peso: Number(row[pesoIndex]),
        cultura: String(row[culturaIndex]),
      }));

      const listaFiltrada = data.filter((item) => item.ncc !== undefined);
      processingOrderData.push(...listaFiltrada);
    }

    tipoArquivoRef.current = TipoArquivo.OrdemBeneficiamento;
    return processingOrderData;
  };

  const extractProductionOrderData = async (
    files: File[]
  ): Promise<ProductionOrder[]> => {
    const productionOrderData: ProductionOrder[] = [];

    for (const file of files) {
      const arrayBuffer = await file.arrayBuffer();
      const workbook = XLSX.read(arrayBuffer);
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];

      const allRows = XLSX.utils.sheet_to_json(sheet, { header: 1 });
      const headers = allRows[0] as string[];

      // Encontrar índices das colunas necessárias
      const nccIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("ncc")
      );
      const linhagemIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("linhagem")
      );
      const opIndex = headers.findIndex((h) => h.toLowerCase().includes("op"));
      const ensaioIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("obs")
      );

      // Validar se todas as colunas necessárias foram encontradas
      if ([nccIndex, linhagemIndex, opIndex, ensaioIndex].includes(-1)) {
        setMessage(
          "Arquivo inválido: Algumas colunas necessárias não foram encontradas"
        );
        return [];
      }

      const data = allRows.slice(1).map((row: any) => ({
        ncc: String(row[nccIndex]),
        linhagem: String(row[linhagemIndex]),
        op: String(row[opIndex]),
        ensaio: String(row[ensaioIndex]),
      }));

      const listaFiltrada = data.filter((item) => item.ncc !== undefined);
      productionOrderData.push(...listaFiltrada);
    }

    tipoArquivoRef.current = TipoArquivo.OrdemProducao;

    return productionOrderData;
  };

  const extractWeightUpdateData = async (
    files: File[]
  ): Promise<ProcessingOrder[]> => {
    const weightUpdateData: ProcessingOrder[] = [];

    for (const file of files) {
      const arrayBuffer = await file.arrayBuffer();
      const workbook = XLSX.read(arrayBuffer);
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];

      const allRows = XLSX.utils.sheet_to_json(sheet, { header: 1 });
      const headers = allRows[0] as string[];

      // Encontrar índices das colunas necessárias
      const culturaIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("cultura")
      );
      const loteIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("lote")
      );
      const nccIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("ncc")
      );
      const linhagemIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("genotipo")
      );
      const pesoIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("peso")
      );

      // Validar se todas as colunas necessárias foram encontradas
      if (
        [culturaIndex, loteIndex, nccIndex, linhagemIndex, pesoIndex].includes(
          -1
        )
      ) {
        //throw new Error('Arquivo inválido: Algumas colunas necessárias não foram encontradas')
        setMessage(
          "Arquivo inválido: Algumas colunas necessárias não foram encontradas"
        );
        return [];
      }

      const data = allRows.slice(1).map((row: any) => ({
        nc: String(null),
        linhagem: String(row[linhagemIndex]),
        ncc: String(row[nccIndex]),
        lote: String(row[loteIndex]),
        peso: Number(row[pesoIndex]),
        cultura: String(row[culturaIndex]),
      }));

      const listaFiltrada = data.filter((item) => item.ncc !== undefined);
      weightUpdateData.push(...listaFiltrada);
    }

    tipoArquivoRef.current = TipoArquivo.OrdemBeneficiamentoAttPeso;
    return weightUpdateData;
  };

  const extractDocumentoEnvelopeContagemData = async (
    files: File[]
  ): Promise<DocumentoEnvelopeContagem[]> => {
    const documentoEnvelopeContagemData: DocumentoEnvelopeContagem[] = [];

    for (const file of files) {
      const arrayBuffer = await file.arrayBuffer();
      const workbook = XLSX.read(arrayBuffer);
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];

      const allRows = XLSX.utils.sheet_to_json(sheet, { header: 1 });
      const headers = allRows[0] as string[];

      // Encontrar índices das colunas necessárias
      const npeIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("npe")
      );
      const ncaIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("nca")
      );
      const statusIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("status")
      );
      const gliIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("gli")
      );
      const codlocalIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("codlocal")
      );
      const linhagemIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("linhagem")
      );

      // Validar se todas as colunas necessárias foram encontradas
      if ([npeIndex, ncaIndex, statusIndex].includes(-1)) {
        setMessage(
          "Arquivo inválido: Algumas colunas necessárias não foram encontradas"
        );
        return [];
      }

      const data = allRows.slice(1).map((row: any) => ({
        npe: String(row[npeIndex]),
        nca: String(row[ncaIndex]),
        status: String(row[statusIndex]),
        gli: String(row[gliIndex]),
        codlocal: String(row[codlocalIndex]),
        linhagem: String(row[linhagemIndex]),
      }));

      const listaFiltrada = data.filter((item) => item.nca !== undefined);
      documentoEnvelopeContagemData.push(...listaFiltrada);
    }

    tipoArquivoRef.current = TipoArquivo.EnvelopeContagem;

    return documentoEnvelopeContagemData;
  };

  const extractDocumentoExpedicaoData = async (
    files: File[]
  ): Promise<DocumentoExpedicao[]> => {
    const documentoExpedicaoData: DocumentoExpedicao[] = [];

    for (const file of files) {
      const arrayBuffer = await file.arrayBuffer();
      const workbook = XLSX.read(arrayBuffer);
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];

      const allRows = XLSX.utils.sheet_to_json(sheet, { header: 1 });
      const headers = allRows[0] as string[];

      // Encontrar índices das colunas necessárias
      const ensaioIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("ensaio")
      );
      const localIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("local")
      );
      const caixaIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("caixa")
      );
      const tagIdIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("tag_id")
      );
      const expedIndex = headers.findIndex((h) =>
        h.toLowerCase().includes("exped")
      );

      // Validar se todas as colunas necessárias foram encontradas
      if (
        [ensaioIndex, localIndex, caixaIndex, tagIdIndex, expedIndex].includes(
          -1
        )
      ) {
        setMessage(
          "Arquivo inválido: Algumas colunas necessárias não foram encontradas"
        );
        return [];
      }

      const data = allRows.slice(1).map((row: any) => ({
        exped: String(row[expedIndex]),
        caixa: String(row[caixaIndex]),
        tag_id: String(row[tagIdIndex]),
        local: String(row[localIndex]),
        ensaio: String(row[ensaioIndex]),
      }));

      const listaFiltrada = data.filter((item) => item.tag_id !== undefined);
      documentoExpedicaoData.push(...listaFiltrada);
    }

    tipoArquivoRef.current = TipoArquivo.Expedicao;

    return documentoExpedicaoData;
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleFiles(event.target.files);
  };

  const handleDragEnter = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);
    const droppedFiles = event.dataTransfer.files;
    handleFiles(droppedFiles);
  };

  useEffect(() => {
    if (harvestOrder.length > 0) {
      handleUploadToSupabase();
    }
  }, [harvestOrder]);

  useEffect(() => {
    if (processingOrder.length > 0) {
      handleUploadToSupabase();
    }
  }, [processingOrder]);

  useEffect(() => {
    if (productionOrder.length > 0) {
      handleUploadToSupabase();
    }
  }, [productionOrder]);

  useEffect(() => {
    if (weightUpdateOrder.length > 0) {
      // persistWeightUpdateToSupabase(weightUpdateOrder, files[0].name);
      handleUploadToSupabase();
    }
  }, [weightUpdateOrder]);

  useEffect(() => {
    if (documentoEnvelopeContagem.length > 0) {
      handleUploadToSupabase();
    }
  }, [documentoEnvelopeContagem]);

  useEffect(() => {
    if (documentoExpedicao.length > 0) {
      handleUploadToSupabase();
    }
  }, [documentoExpedicao]);

  const handleHarvestOrderData = async () => {
    if (files.length === 0) {
      setMessage("Por favor, selecione arquivos para processar.");
      return;
    }

    setIsProcessing(true);
    setMessage("Processando dados...");

    try {
      const harvestOrder = await extractHarvestOrderData(files);
      setHarvestOrder(harvestOrder);
      if (harvestOrder && processingOrder.length > 0) {
        setMessage(
          `${files.length} arquivo(s) processado(s) com sucesso! ${processingOrder.length} registros extraídos.`
        );
      }
    } catch (error) {
      setMessage(
        "Ocorreu um erro ao processar os arquivos. Por favor, tente novamente."
      );
      console.error("Erro ao processar arquivos:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleProcessingOrderData = async () => {
    if (files.length === 0) {
      setMessage("Por favor, selecione arquivos para processar.");
      return;
    }

    setIsProcessing(true);
    setMessage("Processando dados...");

    try {
      const processingOrder = await extractProcessingOrderData(files);
      setProcessingOrder(processingOrder);
      if (processingOrder && processingOrder.length > 0) {
        setMessage(
          `${files.length} arquivo(s) processado(s) com sucesso! ${processingOrder.length} registros extraídos.`
        );
      }
    } catch (error) {
      setMessage(
        "Ocorreu um erro ao processar os arquivos. Por favor, tente novamente."
      );
    } finally {
      setIsProcessing(false);
    }
  };

  const handleProductionOrderData = async () => {
    if (files.length === 0) {
      setMessage("Por favor, selecione arquivos para processar.");
      return;
    }

    setIsProcessing(true);
    setMessage("Processando dados...");

    try {
      const productionOrder = await extractProductionOrderData(files);
      setProductionOrder(productionOrder);

      if (productionOrder && processingOrder.length > 0) {
        setMessage(
          `${files.length} arquivo(s) processado(s) com sucesso! ${processingOrder.length} registros extraídos.`
        );
      }
    } catch (error) {
      setMessage(
        "Ocorreu um erro ao processar os arquivos. Por favor, tente novamente."
      );
      console.error("Erro ao processar arquivos:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleWeightUpdateData = async () => {
    if (files.length === 0) {
      setMessage("Por favor, selecione arquivos para processar.");
      return;
    }

    setIsProcessing(true);
    setMessage("Processando dados...");

    try {
      const weightUpdateData = await extractWeightUpdateData(files);
      setWeightUpdateOrder(weightUpdateData);
      if (weightUpdateData.length > 0) {
        setMessage(
          `${files.length} arquivo(s) processado(s) com sucesso! ${weightUpdateData.length} registros extraídos.`
        );
      }
    } catch (error) {
      setMessage(
        "Ocorreu um erro ao processar os arquivos. Por favor, tente novamente."
      );
      console.error("Erro ao processar arquivos:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDocumentoEnvelopeContagemData = async () => {
    if (files.length === 0) {
      setMessage("Por favor, selecione arquivos para processar.");
      return;
    }

    setIsProcessing(true);
    setMessage("Processando dados...");

    try {
      const documentoEnvelopeContagem =
        await extractDocumentoEnvelopeContagemData(files);
      setDocumentoEnvelopeContagem(documentoEnvelopeContagem);
      if (productionOrder && processingOrder.length > 0) {
        setMessage(
          `${files.length} arquivo(s) processado(s) com sucesso! ${processingOrder.length} registros extraídos.`
        );
      }
    } catch (error) {
      setMessage(
        "Ocorreu um erro ao processar os arquivos. Por favor, tente novamente."
      );
      console.error("Erro ao processar arquivos:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDocumentoExpedicaoData = async () => {
    if (files.length === 0) {
      setMessage("Por favor, selecione arquivos para processar.");
      return;
    }

    setIsProcessing(true);
    setMessage("Processando dados...");

    try {
      const documentoExpedicao = await extractDocumentoExpedicaoData(files);
      setDocumentoExpedicao(documentoExpedicao);
      if (documentoExpedicao && documentoExpedicao.length > 0) {
        setMessage(
          `${files.length} arquivo(s) processado(s) com sucesso! ${processingOrder.length} registros extraídos.`
        );
      }
    } catch (error) {
      setMessage(
        "Ocorreu um erro ao processar os arquivos. Por favor, tente novamente."
      );
      console.error("Erro ao processar arquivos:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleUploadToSupabase = async () => {
    try {
      if (tipoArquivoRef.current === TipoArquivo.OrdemBeneficiamento) {
        if (processingOrder.length === 0) {
          setMessage("Por favor, processe os dados antes de fazer o upload.");
          return;
        }

        persistProcessingOrderToSupabase(processingOrder, files[0].name);

        setIsUploading(true);
        setMessage("Enviando dados para o Servidor...");

        setMessage(
          `${processingOrder.length} registros inseridos com sucesso no Supabase!`
        );
        setProcessingOrder([]);
        setFiles([]);
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      } else if (tipoArquivoRef.current === TipoArquivo.OrdemColheita) {
        if (harvestOrder.length === 0) {
          setMessage("Por favor, processe os dados antes de fazer o upload.");
          return;
        }
        persistHarvestOrderToSupabase(harvestOrder, files[0].name);

        setIsUploading(true);
        setMessage("Enviando dados para o Servidor...");

        setMessage(
          `${harvestOrder.length} registros inseridos com sucesso no Supabase!`
        );
        setHarvestOrder([]);
        setFiles([]);
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      } else if (tipoArquivoRef.current === TipoArquivo.OrdemProducao) {
        if (productionOrder.length === 0) {
          setMessage("Por favor, processe os dados antes de fazer o upload.");
          return;
        }

        persistProductionOrderToSupabase(productionOrder, files[0].name);

        setIsUploading(true);
        setMessage("Enviando dados para o Servidor...");

        setMessage(
          `${productionOrder.length} registros inseridos com sucesso no Supabase!`
        );
        setProductionOrder([]);
        setFiles([]);
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      } else if (tipoArquivoRef.current === TipoArquivo.EnvelopeContagem) {
        if (documentoEnvelopeContagem.length === 0) {
          setMessage("Por favor, processe os dados antes de fazer o upload.");
          return;
        }

        persistEnvelopeContagemToSupabase(
          documentoEnvelopeContagem,
          files[0].name
        );

        setIsUploading(true);
        setMessage("Enviando dados para o Servidor...");

        setMessage(
          `${documentoEnvelopeContagem.length} registros inseridos com sucesso no Supabase!`
        );
        setDocumentoEnvelopeContagem([]);
        setFiles([]);
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      } else if (tipoArquivoRef.current === TipoArquivo.Expedicao) {
        if (documentoExpedicao.length === 0) {
          setMessage("Por favor, processe os dados antes de fazer o upload.");
          return;
        }

        persistCaixasExpedicaoToSupabase(documentoExpedicao, files[0].name);

        setIsUploading(true);
        setMessage("Enviando dados para o Servidor...");

        setMessage(
          `${documentoExpedicao.length} registros inseridos com sucesso no Supabase!`
        );
        setDocumentoExpedicao([]);
        setFiles([]);
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      } else if (
        tipoArquivoRef.current === TipoArquivo.OrdemBeneficiamentoAttPeso
      ) {
        if (weightUpdateOrder.length === 0) {
          setMessage("Por favor, processe os dados antes de fazer o upload.");
          return;
        }
        persistWeightUpdateToSupabase(weightUpdateOrder, files[0].name);

        setIsUploading(true);
        setMessage("Enviando dados para o Servidor...");

        setMessage(
          `Registros processados e inseridos com sucesso no Supabase!`
        );
        setWeightUpdateOrder([]);
        setFiles([]);
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      }
    } catch (error) {
      setMessage(
        "Ocorreu um erro ao enviar os dados para o Supabase. Por favor, tente novamente."
      );
      console.error("Erro ao enviar dados para o Supabase:", error);
    } finally {
      setIsUploading(false);
    }
  };

  const removeFile = (index: number) => {
    setFiles(files.filter((_, i) => i !== index));
    setHarvestOrder([]); // Limpa os dados extraídos quando um arquivo é removido
    setProcessingOrder([]);
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Upload e Extração de Dados</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div
            ref={dropZoneRef}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            className={`border-2 border-dashed rounded-lg p-4 text-center transition-colors ${
              isDragging
                ? "border-primary bg-primary/10"
                : "border-muted-foreground"
            }`}
          >
            {files.length === 0 ? (
              <>
                <Upload className="mx-auto h-12 w-12 text-muted-foreground" />
                <p className="mt-2 text-sm text-muted-foreground">
                  Arraste e solte seus arquivos aqui, ou clique para selecionar
                </p>
                <p className="mt-1 text-xs text-muted-foreground">
                  (Apenas arquivos XLS, XLSX e CSV são permitidos)
                </p>
              </>
            ) : (
              <div className="flex flex-col items-center">
                <FileIcon className="h-12 w-12 text-primary" />
                <p className="mt-2 font-medium text-foreground">
                  {files.length === 1
                    ? files[0].name
                    : `${files.length} arquivos selecionados`}
                </p>
              </div>
            )}
            <Input
              type="file"
              multiple
              className="hidden"
              onChange={handleFileChange}
              ref={fileInputRef}
              accept=".xls,.xlsx,.csv"
              aria-label="Entrada de arquivo"
            />
            <Button
              onClick={() => fileInputRef.current?.click()}
              variant="outline"
              className="mt-4"
            >
              {files.length === 0
                ? "Selecionar Arquivos"
                : "Selecionar Arquivos Diferentes"}
            </Button>
          </div>
          {files.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Arquivos Selecionados:</h3>
              {files.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between bg-secondary p-2 rounded-md"
                >
                  <span className="truncate max-w-[200px]">
                    {file.name} (.
                    {
                      FILE_TYPE_EXTENSIONS[
                        file.type as keyof typeof FILE_TYPE_EXTENSIONS
                      ]
                    }
                    )
                  </span>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => removeFile(index)}
                    aria-label={`Remover ${file.name}`}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
          <div className="flex gap-2">
            <Button
              onClick={handleHarvestOrderData}
              className="flex-1"
              disabled={isProcessing || files.length === 0}
            >
              {isProcessing
                ? "Processando..."
                : "Processar Ordem de Beneficiamento"}
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={async () => {
                const response = await fetch("/api/download/harvest-template");
                if (response.status === 404) {
                  setMessage("Arquivo não encontrado!");
                } else if (response.ok) {
                  window.open("/api/download/harvest-template", "_blank");
                  setMessage("Arquivo baixado com sucesso!");
                }
              }}
              title="Baixar modelo de Ordem de Colheita"
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex gap-2">
            <Button
              onClick={handleProcessingOrderData}
              className="w-full"
              disabled={isProcessing || files.length === 0}
            >
              {isProcessing
                ? "Processando..."
                : "Processar Pesos de Beneficiamento"}
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={async () => {
                const response = await fetch(
                  "/api/download/processing-template"
                );
                if (response.status === 404) {
                  setMessage("Arquivo não encontrado!");
                } else if (response.ok) {
                  window.open("/api/download/processing-template", "_blank");
                  setMessage("Arquivo baixado com sucesso!");
                }
              }}
              title="Baixar modelo de Ordem de Beneficiamento"
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex gap-2">
            <Button
              onClick={handleWeightUpdateData}
              className="w-full"
              disabled={isProcessing || files.length === 0}
            >
              {isProcessing
                ? "Processando..."
                : "Processar Att. de Pesos de Beneficiamento"}
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={async () => {
                const response = await fetch(
                  "/api/download/processing-template"
                );
                if (response.status === 404) {
                  setMessage("Arquivo não encontrado!");
                } else if (response.ok) {
                  window.open("/api/download/processing-template", "_blank");
                  setMessage("Arquivo baixado com sucesso!");
                }
              }}
              title="Baixar modelo de Ordem de Beneficiamento"
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex gap-2">
            <Button
              onClick={handleProductionOrderData}
              className="w-full"
              disabled={isProcessing || files.length === 0}
            >
              {isProcessing ? "Processando..." : "Processar Ordem de Produção"}
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={async () => {
                const response = await fetch(
                  "/api/download/production-template"
                );
                if (response.status === 404) {
                  setMessage("Arquivo não encontrado!");
                } else if (response.ok) {
                  window.open("/api/download/production-template", "_blank");
                  setMessage("Arquivo baixado com sucesso!");
                }
              }}
              title="Baixar modelo de Ordem de Produção"
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex gap-2">
            <Button
              onClick={handleDocumentoEnvelopeContagemData}
              className="w-full"
              disabled={isProcessing || files.length === 0}
            >
              {isProcessing ? "Processando..." : "Processar Etiquetagem"}
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={async () => {
                const response = await fetch("/api/download/envelope-template");
                if (response.status === 404) {
                  setMessage("Arquivo não encontrado!");
                } else if (response.ok) {
                  window.open("/api/download/envelope-template", "_blank");
                  setMessage("Arquivo baixado com sucesso!");
                }
              }}
              title="Baixar modelo de Etiquetagem"
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex gap-2">
            <Button
              onClick={handleDocumentoExpedicaoData}
              className="w-full"
              disabled={isProcessing || files.length === 0}
            >
              {isProcessing ? "Processando..." : "Processar Expedição"}
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={async () => {
                const response = await fetch(
                  "/api/download/expedicao-template"
                );
                if (response.status === 404) {
                  setMessage("Arquivo não encontrado!");
                } else if (response.ok) {
                  window.open("/api/download/expedicao-template", "_blank");
                  setMessage("Arquivo baixado com sucesso!");
                }
              }}
              title="Baixar modelo de Caixas De Expedição"
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
          {message && (
            <Alert>
              <AlertDescription>{message}</AlertDescription>
            </Alert>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
