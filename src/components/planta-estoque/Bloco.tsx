import React from "react";
import { EnderecoEstoque } from "./types";
import Baia from "./Baia";

interface BlocoProps {
  enderecos: EnderecoEstoque[];
  onEnderecoClick?: (endereco: EnderecoEstoque) => void;
  onEnderecoHover?: (endereco: EnderecoEstoque) => void;
  className?: string;
}

const Bloco: React.FC<BlocoProps> = ({
  enderecos,
  onEnderecoClick,
  onEnderecoHover,
  className = "",
}) => {
  // Agrupa endereços por baia dentro do bloco (normalmente cada endereço é uma baia)
  // Mas mantém estrutura para possível expansão
  return (
    <div className={`w-full bg-yellow-100 p-4 rounded-sm ${className}`}>
      <div className="mb-3 text-center font-bold">
        {enderecos[0]?.bloco.nome}
      </div>
      <div className="flex flex-col gap-y-2">
        {enderecos.map((endereco) => (
          <Baia
            key={endereco.baia.id}
            endereco={endereco}
            onClick={onEnderecoClick}
            onHover={onEnderecoHover}
            className="baia-container"
          />
        ))}
      </div>
    </div>
  );
};

export default Bloco;
