import React, { useState } from "react";
import { EnderecoEstoque } from "./types";
import { useRouter } from "next/navigation";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";

interface BaiaProps {
  endereco: EnderecoEstoque;
  onClick?: (endereco: EnderecoEstoque) => void;
  onHover?: (endereco: EnderecoEstoque) => void;
  className?: string;
}

const Baia: React.FC<BaiaProps> = ({
  endereco,
  onClick,
  onHover,
  className = "",
}) => {
  const router = useRouter();
  const [isHovered, setIsHovered] = useState(false);
  const [isClicked, setIsClicked] = useState(false);

  let statusTailwind =
    endereco.status === "livre"
      ? "bg-green-600 text-white border-green-800"
      : endereco.status === "ocupado"
      ? "bg-red-600 text-white border-red-800"
      : "bg-yellow-300 text-gray-900 border-yellow-600";

  // Handlers de interação
  const handleMouseEnter = () => {
    setIsHovered(true);
    if (onHover) onHover(endereco);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setIsClicked(false);
  };

  // Função para redirecionar para a página de alocação
  const handleAlocacao = () => {
    const queryParams = new URLSearchParams({
      rua_id: endereco.rua.id,
      bloco_id: endereco.bloco.id,
      baia_id: endereco.baia.id,
    }).toString();

    router.push(`/estoque/alocacoes/new?${queryParams}`);
  };

  // Função para redirecionar para a página de desalocação com o lote selecionado
  const handleDesalocacao = (lote: any) => {
    const queryParams = new URLSearchParams({
      lote_id: lote.id,
      numero_lote: lote.numero_lote,
    }).toString();

    router.push(`/estoque/desalocacoes/new?${queryParams}`);
  };

  // Função para lidar com o clique na baia (para manter compatibilidade com o onClick)
  const handleClick = () => {
    setIsClicked(true);
    if (onClick) onClick(endereco);
  };

  // Renderização com Context Menu para todos os casos
  return (
    <ContextMenu>
      <ContextMenuTrigger>
        <div
          className={`relative min-w-16 min-h-16 m-1 rounded-sm flex flex-col items-center justify-center font-medium shadow transition-shadow duration-200 cursor-pointer border-2 ${statusTailwind} ${className}`}
          onClick={handleClick}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          tabIndex={0}
        >
          <div className="baia-nome">{endereco.baia.nome}</div>
          {endereco.lotes && endereco.lotes.length > 0 ? (
            <div className="baia-lote">
              <span>
                {endereco.lotes.length === 1 ? "Lote" : "Lotes"}:{" "}
                {endereco.lotes.length}
              </span>
            </div>
          ) : (
            <div className="baia-livre">Livre</div>
          )}

          {/* Tooltip de detalhes ao passar o mouse */}
          {isHovered && (
            <div
              className="absolute left-1/2 top-0 z-10 -translate-x-1/2 -translate-y-full bg-white text-black border border-gray-300 rounded-lg shadow-lg p-3 min-w-[180px] max-w-xs"
              style={{ pointerEvents: "auto" }}
            >
              <strong>Detalhes do Endereço</strong>
              <div>
                Rua: {endereco.rua.nome} <br />
                Bloco: {endereco.bloco.nome} <br />
                Baia: {endereco.baia.nome} <br />
                Status: {endereco.status}
              </div>
              {endereco.lotes && endereco.lotes.length > 0 ? (
                <div>
                  <br />
                  <strong>
                    {endereco.lotes.length === 1 ? "Lote:" : "Lotes:"}
                  </strong>{" "}
                  {endereco.lotes.map((lote, idx) => (
                    <span key={lote.id}>
                      {lote.numero_lote}
                      {idx < (endereco.lotes?.length ?? 0) - 1 ? ", " : ""}
                    </span>
                  ))}
                </div>
              ) : (
                <div>
                  <br />
                  <em>Sem lote alocado</em>
                </div>
              )}
            </div>
          )}
        </div>
      </ContextMenuTrigger>
      <ContextMenuContent className="w-64">
        <div className="px-2 py-1.5 text-sm font-semibold">
          {endereco.status === "livre"
            ? "Ações disponíveis"
            : "Selecione uma ação"}
        </div>

        {/* Menu para baia livre */}
        {endereco.status === "livre" && (
          <ContextMenuItem onClick={handleAlocacao}>
            Alocar novo lote
          </ContextMenuItem>
        )}

        {/* Menu para baia ocupada */}
        {endereco.status === "ocupado" && endereco.lotes && (
          <>
            {endereco.lotes.length === 1 ? (
              // Caso de um único lote
              <ContextMenuItem
                onClick={() =>
                  endereco.lotes && handleDesalocacao(endereco.lotes[0])
                }
              >
                Desalocar lote: {endereco.lotes[0].numero_lote}
              </ContextMenuItem>
            ) : (
              // Caso de múltiplos lotes
              endereco.lotes.map((lote) => (
                <ContextMenuItem
                  key={lote.id}
                  onClick={() => handleDesalocacao(lote)}
                >
                  Desalocar lote: {lote.numero_lote}
                </ContextMenuItem>
              ))
            )}
          </>
        )}
      </ContextMenuContent>
    </ContextMenu>
  );
};

export default Baia;
