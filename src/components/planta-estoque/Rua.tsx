import React from "react";
import { EnderecoEstoque } from "./types";
import Bloco from "./Bloco";

interface RuaProps {
  enderecos: EnderecoEstoque[];
  onEnderecoClick?: (endereco: EnderecoEstoque) => void;
  onEnderecoHover?: (endereco: EnderecoEstoque) => void;
  className?: string;
}

const Rua: React.FC<RuaProps> = ({
  enderecos,
  onEnderecoClick,
  onEnderecoHover,
  className = "",
}) => {
  // Agrupa endereços por bloco dentro da rua
  const blocos = React.useMemo(() => {
    const map = new Map<string, EnderecoEstoque[]>();
    enderecos.forEach((endereco) => {
      const key = endereco.bloco.id;
      if (!map.has(key)) map.set(key, []);
      map.get(key)!.push(endereco);
    });
    return Array.from(map.values());
  }, [enderecos]);

  return (
    <div className={`bg-blue-100 w-full p-4 rounded-sm ${className}`}>
      <div className="mb-4 text-center font-bold">{enderecos[0]?.rua.nome}</div>
      <div className="flex flex-col w-full gap-y-4">
        {blocos.map((enderecosBloco, idx) => (
          <Bloco
            key={enderecosBloco[0].bloco.id}
            enderecos={enderecosBloco}
            onEnderecoClick={onEnderecoClick}
            onEnderecoHover={onEnderecoHover}
            className="bloco-container"
          />
        ))}
      </div>
    </div>
  );
};

export default Rua;
