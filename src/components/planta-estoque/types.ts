import type { Lote } from "../../data/model/Lote";

export type EnderecoStatus = "livre" | "ocupado" | "bloqueado";

export interface EnderecoEstoque {
  rua: { id: string; nome: string };
  bloco: { id: string; nome: string };
  baia: { id: string; nome: string };
  status: EnderecoStatus;
  lotes?: Lote[];
}

export interface PlantaEstoqueProps {
  enderecos: EnderecoEstoque[];
  onEnderecoClick?: (endereco: EnderecoEstoque) => void;
  onEnderecoHover?: (endereco: EnderecoEstoque) => void;
}
