import React from "react";
import { EnderecoEstoque, PlantaEstoqueProps } from "./types";
import Rua from "./Rua";

const PlantaEstoque: React.FC<PlantaEstoqueProps> = ({
  enderecos,
  onEnderecoClick,
  onEnderecoHover,
}) => {
  // Agrupa endereços por rua
  const ruas = React.useMemo(() => {
    const map = new Map<string, EnderecoEstoque[]>();
    enderecos.forEach((endereco) => {
      const key = endereco.rua.id;
      if (!map.has(key)) map.set(key, []);
      map.get(key)!.push(endereco);
    });
    return Array.from(map.values());
  }, [enderecos]);

  return (
    <div className="flex flex-row gap-x-4 py-2">
      {ruas.map((enderecosRua) => (
        <Rua
          key={enderecosRua[0].rua.id}
          enderecos={enderecosRua}
          onEnderecoClick={onEnderecoClick}
          onEnderecoHover={onEnderecoHover}
          className="flex flex-col items-center min-w-[200px] h-full"
        />
      ))}
    </div>
  );
};

export default PlantaEstoque;
