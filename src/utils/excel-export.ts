import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'

export function exportToExcel<T>({ 
  data, 
  filename, 
  sheetName = 'Sheet1' 
}: {
  data: T[]
  filename: string
  sheetName?: string
}) {
  const ws = XLSX.utils.json_to_sheet(data)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, sheetName)
  
  const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' })
  const blob = new Blob([excelBuffer], { 
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
  })
  
  saveAs(blob, `${filename}_${new Date().toISOString().split('T')[0]}.xlsx`)
} 